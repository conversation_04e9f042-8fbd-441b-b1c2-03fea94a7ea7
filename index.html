<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Journal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="apple-touch-icon" sizes="180x180" href="/static/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/static/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/favicon/favicon-16x16.png">
    <link rel="manifest" href="/static/favicon/site.webmanifest">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f3f4f6;
        }
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8; 
        }
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #09f;
            animation: spin 1s ease infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .chat-bubble {
            max-width: 75%;
            padding: 10px 15px;
            border-radius: 15px;
            margin-bottom: 10px;
            word-wrap: break-word;
        }
        .chat-bubble-user {
            background-color: #3b82f6;
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }
        .chat-bubble-model {
            background-color: #e5e7eb;
            color: #1f2937;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 50;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.6);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 25px;
            border: 1px solid #888;
            width: 80%;
            max-width: 500px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .modal-close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .modal-close:hover,
        .modal-close:focus {
            color: black;
            text-decoration: none;
        }

        /* Mobile menu styles */
        .mobile-menu {
            transition: all 0.3s ease-in-out;
        }

        .mobile-menu.hidden {
            opacity: 0;
            transform: translateY(-10px);
        }

        .mobile-menu:not(.hidden) {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body class="text-gray-800">
    <nav class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2">
                    <img src="/static/logo.png" alt="Voice Diary Logo" class="w-8 h-8 rounded-lg object-contain">
                    <span class="text-xl font-bold text-gray-900">Voice Diary</span>
                </div>


                <div class="hidden md:flex items-center space-x-8">
                    <a href="/home" class="text-gray-600 hover:text-blue-500 transition-colors">Home</a>
                    <a href="/pricing" class="text-gray-600 hover:text-blue-500 transition-colors">Pricing</a>
                    <a href="/setting" class="text-gray-600 hover:text-blue-500 transition-colors">Settings</a>
                    <button id="logoutButton" type="button" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                        Logout
                    </button>
                </div>

                <button id="mobileMenuBtn" class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>


            <div id="mobileMenu" class="mobile-menu md:hidden bg-white border-t border-gray-200 mt-4 pt-4 hidden">
                <div class="space-y-2">
                    <a href="/home" class="block py-2 text-gray-600 hover:text-blue-500 transition-colors">Home</a>
                    <a href="/pricing" class="block py-2 text-gray-600 hover:text-blue-500 transition-colors">Pricing</a>
                    <a href="/setting" class="block py-2 text-gray-600 hover:text-blue-500 transition-colors">Settings</a>
                    <button id="logoutButtonMobile" type="button" class="w-full mt-2 bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors text-left">
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto p-4 md:p-8 max-w-6xl">
        <header class="mb-8">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900">What's on your mind today?</h1>
            
        </header>

        <div id="loadingIndicator" class="fixed top-4 right-4 z-50 hidden">
            <div class="spinner"></div>
        </div>

        <div id="statusMessage" class="mb-4 p-3 rounded-md text-center hidden"></div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">

            <div class="md:col-span-1 space-y-6">
                <div class="bg-white p-5 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-3 text-gray-700">Purpose</h2>
                    <div class="flex items-center space-x-2">
                        <select id="purposeSelect" class="block w-full mt-1 rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-2 px-3 flex-grow">
                            <option value="0">-- Select Purpose --</option>
                            <option value="-1">-- View All Entries --</option>
                            </select>
                        <button id="addPurposeBtn" title="Add New Purpose" class="p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                    <div id="purposeDescription" class="mt-3 text-sm text-gray-500 italic"></div>
                </div>

                <div class="bg-white p-5 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-3 text-gray-700">Record New Entry</h2>
                    <div class="flex items-center justify-center space-x-4 mb-4">
                        <button id="recordButton" class="px-6 py-3 bg-red-500 text-white font-semibold rounded-full shadow-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed">
                            Start Recording
                        </button>
                        <div id="timer" class="text-lg font-mono text-gray-600">00:00</div>
                    </div>
                    <canvas id="visualizer" class="w-full h-20 bg-gray-200 rounded-md"></canvas>
                    <p class="text-xs text-gray-500 mt-2 text-center">Select a purpose before recording.</p>
                </div>
            </div>

            <div class="md:col-span-2 space-y-6">
                <div class="bg-white p-5 rounded-lg shadow">
                    <h2 class="text-xl font-semibold mb-3 text-gray-700">Journal Entries</h2>
                    <div id="entriesList" class="space-y-4 max-h-[600px] overflow-y-auto pr-2">
                        <p id="noEntriesMessage" class="text-gray-500 italic">No entries found. Record one!</p>
                    </div>
                </div>

                <div id="chatSection" class="bg-white p-5 rounded-lg shadow hidden">
                    <h2 class="text-xl font-semibold mb-3 text-gray-700">Chat about <span id="chatPurposeName" class="font-bold">Purpose</span></h2>
                    <div id="chatHistory" class="space-y-2 h-64 overflow-y-auto mb-4 border border-gray-200 rounded-md p-3 bg-gray-50">
                        <p id="noChatMessage" class="text-gray-500 italic text-center">No chat history yet. Send a message!</p>
                    </div>
                    <div class="flex space-x-2">
                        <input type="text" id="chatInput" placeholder="Ask about your entries..." class="flex-grow mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm px-3 py-2" disabled>
                        <button id="sendChatButton" class="px-4 py-2 bg-green-500 text-white font-semibold rounded-md shadow-sm hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            Send
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="addPurposeModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" id="closeModalBtn">&times;</span>
            <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">Add New Purpose</h3>
            <div>
                <label for="newPurposeName" class="block text-sm font-medium text-gray-700">Purpose Name</label>
                <input type="text" id="newPurposeName" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2" placeholder="e.g., Daily Reflections">
            </div>
            <div class="mt-4">
                <label for="newPurposeDescription" class="block text-sm font-medium text-gray-700">Description (Optional)</label>
                <textarea id="newPurposeDescription" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm px-3 py-2" placeholder="A brief description of this purpose..."></textarea>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button id="cancelModalBtn" type="button" class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    Cancel
                </button>
                <button id="savePurposeBtn" type="button" class="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Save Purpose
                </button>
            </div>
        </div>
    </div>

    <!-- Report a Bug Button -->
    <div class="fixed bottom-4 right-4 z-50">
        <a href="https://wa.me/254768409911?text=Hi%2C%20I%20found%20a%20bug%20in%20the%20Voice%20Journal%20app%3A%20"
           target="_blank"
           rel="noopener noreferrer"
           class="inline-flex items-center justify-center w-12 h-12 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 group"
           title="Report a Bug via WhatsApp">
            <!-- WhatsApp Icon -->
            <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
            </svg>
            <!-- Bug Icon (small overlay) -->
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <svg class="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"/>
                </svg>
            </div>
        </a>
    </div>

    <script src="app.js"></script>
</body>
</html>
