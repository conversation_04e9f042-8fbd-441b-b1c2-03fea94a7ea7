
const API_BASE_URL = '';
const RECORDING_TIME_LIMIT_MS = 5 * 60 * 1000; 


const purposeSelect = document.getElementById('purposeSelect');
const addPurposeBtn = document.getElementById('addPurposeBtn');
const purposeDescription = document.getElementById('purposeDescription');
const recordButton = document.getElementById('recordButton');
const timerDisplay = document.getElementById('timer');
const visualizerCanvas = document.getElementById('visualizer');
const entriesList = document.getElementById('entriesList');
const noEntriesMessage = document.getElementById('noEntriesMessage');
const chatSection = document.getElementById('chatSection');
const chatPurposeName = document.getElementById('chatPurposeName');
const chatHistory = document.getElementById('chatHistory');
const noChatMessage = document.getElementById('noChatMessage');
const chatInput = document.getElementById('chatInput');
const sendChatButton = document.getElementById('sendChatButton');
const statusMessage = document.getElementById('statusMessage');
const loadingIndicator = document.getElementById('loadingIndicator');
const addPurposeModal = document.getElementById('addPurposeModal');
const closeModalBtn = document.getElementById('closeModalBtn');
const cancelModalBtn = document.getElementById('cancelModalBtn');
const savePurposeBtn = document.getElementById('savePurposeBtn');
const newPurposeNameInput = document.getElementById('newPurposeName');
const newPurposeDescriptionInput = document.getElementById('newPurposeDescription');


let mediaRecorder;
let audioChunks = [];
let recordingStartTime;
let timerInterval;
let audioContext;
let analyser;
let source;
let dataArray;
let drawVisual;
let currentMimeType = '';
let isRecording = false;


let purposes = [];
let currentPurposeId = 0;


const canvasCtx = visualizerCanvas.getContext('2d');



function showStatus(message, isError = false) {
    console.log(`Status (${isError ? 'Error' : 'Info'}): ${message}`);
    statusMessage.textContent = message;
    statusMessage.className = `mb-4 p-3 rounded-md text-center text-sm ${
        isError
            ? 'bg-red-100 text-red-700'
            : 'bg-blue-100 text-blue-700'
    }`;
    statusMessage.classList.remove('hidden');

    setTimeout(() => {
        statusMessage.classList.add('hidden');
    }, 5000);
}


function setLoading(show) {
    if (show) {
        loadingIndicator.classList.remove('hidden');
    } else {
        loadingIndicator.classList.add('hidden');
    }
}

function formatTime(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const minutes = Math.floor(totalSeconds / 60).toString().padStart(2, '0');
    const seconds = (totalSeconds % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
}


function updateTimer() {
    const elapsedTime = Date.now() - recordingStartTime;
    timerDisplay.textContent = formatTime(elapsedTime);
    if (elapsedTime >= RECORDING_TIME_LIMIT_MS) {
        stopRecording();
        showStatus("Recording limit reached.", true);
    }
}


function draw() {

    drawVisual = requestAnimationFrame(draw);

    analyser.getByteFrequencyData(dataArray);

    canvasCtx.fillStyle = '#e5e7eb';
    canvasCtx.fillRect(0, 0, visualizerCanvas.width, visualizerCanvas.height);

    const barWidth = (visualizerCanvas.width / analyser.frequencyBinCount) * 2.5;
    let barHeight;
    let x = 0;

    for (let i = 0; i < analyser.frequencyBinCount; i++) {
        barHeight = dataArray[i] * (visualizerCanvas.height / 255);

        canvasCtx.fillStyle = `rgb(${barHeight + 100}, 50, 50)`; 
        canvasCtx.fillRect(x, visualizerCanvas.height - barHeight, barWidth, barHeight);

        x += barWidth + 1; 
    }
}

async function fetchPurposes() {
    setLoading(true);
    try {
        const response = await fetch(`${API_BASE_URL}/api/purposes`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        purposes = await response.json();
        populatePurposeSelect();

        const savedPurposeId = localStorage.getItem('selectedPurposeId');
        if (savedPurposeId && purposes.some(p => p.id === parseInt(savedPurposeId))) {
            purposeSelect.value = savedPurposeId;
            currentPurposeId = parseInt(savedPurposeId);
        } else {
             purposeSelect.value = "0";
             currentPurposeId = 0;
        }
        updatePurposeSelection();
    } catch (error) {
        showStatus(`Error fetching purposes: ${error.message}`, true);
        console.error("Fetch purposes error:", error);
    } finally {
        setLoading(false);
    }
}


async function createPurpose(name, description) {
    setLoading(true);
    try {
        const response = await fetch(`${API_BASE_URL}/api/purposes`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name, description }),
        });
        if (!response.ok) {
            const errorData = await response.text();
            throw new Error(`HTTP error! status: ${response.status} - ${errorData}`);
        }
        const newPurpose = await response.json();
        showStatus(`Purpose "${newPurpose.name}" created successfully!`);
        await fetchPurposes();
        purposeSelect.value = newPurpose.id.toString();
        updatePurposeSelection();
        closePurposeModal();
    } catch (error) {
        showStatus(`Error creating purpose: ${error.message}`, true);
        console.error("Create purpose error:", error);
    } finally {
        setLoading(false);
    }
}

async function fetchEntries(purposeId) {
    setLoading(true);
    entriesList.innerHTML = '';
    noEntriesMessage.classList.add('hidden');
    let url = `${API_BASE_URL}/api/entries`;
    if (purposeId > 0) {
        url += `?purposeId=${purposeId}`;
    }

    try {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const entries = await response.json();
        displayEntries(entries);
    } catch (error) {
        showStatus(`Error fetching entries: ${error.message}`, true);
        console.error("Fetch entries error:", error);
        noEntriesMessage.textContent = "Error loading entries.";
        noEntriesMessage.classList.remove('hidden');
    } finally {
        setLoading(false);
    }
}

async function fetchChatHistory(purposeId) {
    setLoading(true);
    chatHistory.innerHTML = '';
    noChatMessage.classList.add('hidden');

    if (purposeId <= 0) {
         console.warn("Cannot fetch chat history without a valid purpose ID.");
         setLoading(false);
         return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/api/chat/${purposeId}/history`);
        if (!response.ok) {

            if (response.status === 404) {
                console.log(`No chat history found for purpose ${purposeId}.`);
                noChatMessage.classList.remove('hidden');
                return;
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const history = await response.json();
        displayChatHistory(history);
    } catch (error) {
        showStatus(`Error fetching chat history: ${error.message}`, true);
        console.error("Fetch chat history error:", error);
        noChatMessage.textContent = "Error loading chat history.";
        noChatMessage.classList.remove('hidden');
    } finally {
        setLoading(false);
    }
}


async function sendChatMessage(purposeId, message) {
    if (!message.trim() || purposeId <= 0) return;

    setLoading(true);
    chatInput.disabled = true;
    sendChatButton.disabled = true;


    appendChatMessage('user', message);
    chatInput.value = '';

    try {
        const response = await fetch(`${API_BASE_URL}/api/chat/${purposeId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ message: message }),
        });
        if (!response.ok) {
             const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
        }
        const data = await response.json();
        appendChatMessage('model', data.reply);
    } catch (error) {
        showStatus(`Error sending chat message: ${error.message}`, true);
        console.error("Send chat message error:", error);

        appendChatMessage('model', `Sorry, I encountered an error: ${error.message}`);
    } finally {
        setLoading(false);
        chatInput.disabled = false;
        sendChatButton.disabled = false;
        chatInput.focus();
    }
}


async function uploadAudio(audioBlob, mimeType, purposeId) {
    setLoading(true);
    showStatus("Uploading and processing entry...", false);

    const formData = new FormData();
    formData.append('audio', audioBlob, `recording.${mimeType.split('/')[1] || 'webm'}`);
    formData.append('mimeType', mimeType);
    formData.append('purposeId', purposeId.toString());

    try {
        const response = await fetch(`${API_BASE_URL}/api/entries`, {
            method: 'POST',
            body: formData,
        });

        const responseData = await response.json();

        if (!response.ok) {
            throw new Error(responseData.message || `HTTP error! status: ${response.status}`);
        }

        showStatus("Entry saved successfully!", false);
        if (currentPurposeId === -1 || currentPurposeId === purposeId) {
             fetchEntries(currentPurposeId);
        }

    } catch (error) {
        showStatus(`Error saving entry: ${error.message}`, true);
        console.error("Upload audio error:", error);
    } finally {
        setLoading(false);
    }
}



function populatePurposeSelect() {
    while (purposeSelect.options.length > 2) {
        purposeSelect.remove(2);
    }

    purposes.forEach(purpose => {
        const option = document.createElement('option');
        option.value = purpose.id;
        option.textContent = purpose.name;
        option.dataset.description = purpose.description || "";
        purposeSelect.appendChild(option);
    });
}


function updatePurposeSelection() {
    currentPurposeId = parseInt(purposeSelect.value);
    localStorage.setItem('selectedPurposeId', currentPurposeId.toString());

    const selectedOption = purposeSelect.options[purposeSelect.selectedIndex];
    const description = selectedOption.dataset.description;

    if (description) {
        purposeDescription.textContent = description;
        purposeDescription.classList.remove('hidden');
    } else {
        purposeDescription.classList.add('hidden');
    }


    if (currentPurposeId > 0 && !isRecording) {
        recordButton.disabled = false;
    } else {
        recordButton.disabled = true;
    }

    fetchEntries(currentPurposeId);

    if (currentPurposeId > 0) {
        chatSection.classList.remove('hidden');
        chatPurposeName.textContent = selectedOption.textContent;
        chatInput.disabled = false;
        sendChatButton.disabled = false;
        fetchChatHistory(currentPurposeId);
    } else {
        chatSection.classList.add('hidden');
        chatInput.disabled = true;
        sendChatButton.disabled = true;
        chatHistory.innerHTML = '';
        noChatMessage.classList.remove('hidden');
        noChatMessage.textContent = "Select a purpose to start chatting.";
    }
}


function displayEntries(entries) {
    entriesList.innerHTML = '';
    if (!entries || entries.length === 0) {
        noEntriesMessage.textContent = currentPurposeId === 0
            ? "Select a purpose to view entries."
            : "No entries found for this purpose. Record one!";
        noEntriesMessage.classList.remove('hidden');
        return;
    }

    noEntriesMessage.classList.add('hidden');


    entries.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));


    entries.forEach(entry => {
        const entryDiv = document.createElement('div');
        entryDiv.className = 'p-4 border border-gray-200 rounded-lg bg-white hover:shadow-md transition-shadow duration-200';

        const purposeBadge = currentPurposeId <= 0 
            ? `<span class="text-xs font-medium mr-2 px-2.5 py-0.5 rounded bg-blue-100 text-blue-800">${entry.purposeName}</span>`
            : '';

        entryDiv.innerHTML = `
            <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-500">${new Date(entry.createdAt).toLocaleString()}</span>
                ${purposeBadge}
            </div>
            <p class="text-sm text-gray-800 mb-2"><strong>Summary:</strong> ${entry.summary || 'N/A'}</p>
            <details class="text-sm text-gray-600 mb-3">
                <summary class="cursor-pointer text-indigo-600 hover:text-indigo-800">Show Transcription</summary>
                <p class="mt-1 p-2 bg-gray-50 rounded border border-gray-200 whitespace-pre-wrap">${entry.transcription || 'N/A'}</p>
            </details>
            <audio controls class="w-full h-10 mt-2" src="${API_BASE_URL}/audio/${entry.audioFilename}" preload="metadata">
                Your browser does not support the audio element.
            </audio>
        `;
        entriesList.appendChild(entryDiv);
    });
}

function displayChatHistory(history) {
    chatHistory.innerHTML = '';
    if (!history || history.length === 0) {
        noChatMessage.classList.remove('hidden');
        return;
    }
    noChatMessage.classList.add('hidden');
    history.forEach(msg => appendChatMessage(msg.role, msg.content, false));
}


function appendChatMessage(role, content, scroll = true) {
    noChatMessage.classList.add('hidden'); 
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('chat-bubble', role === 'user' ? 'chat-bubble-user' : 'chat-bubble-model');

    content = content
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') 
        .replace(/\*(.*?)\*/g, '<em>$1</em>'); 

    messageDiv.innerHTML = content;
    chatHistory.appendChild(messageDiv);

    if (scroll) {
        chatHistory.scrollTop = chatHistory.scrollHeight;
    }
}



async function startRecording() {
    if (currentPurposeId <= 0) {
        showStatus("Please select a purpose before recording.", true);
        return;
    }

    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        isRecording = true;
        recordButton.textContent = 'Stop Recording';
        recordButton.classList.remove('bg-red-500', 'hover:bg-red-600');
        recordButton.classList.add('bg-yellow-500', 'hover:bg-yellow-600');
        purposeSelect.disabled = true;
        addPurposeBtn.disabled = true;

        const options = { mimeType: 'audio/webm;codecs=opus' };
        try {
            mediaRecorder = new MediaRecorder(stream, options);
        } catch (e1) {
            console.warn(`MediaRecorder with ${options.mimeType} failed: ${e1}. Trying default.`);
            try {
                mediaRecorder = new MediaRecorder(stream);
            } catch (e2) {
                 console.error("MediaRecorder is not supported by this browser.", e2);
                 showStatus("Audio recording is not supported by your browser.", true);
                 isRecording = false;
                 recordButton.textContent = 'Start Recording';
                 recordButton.classList.remove('bg-yellow-500', 'hover:bg-yellow-600');
                 recordButton.classList.add('bg-red-500', 'hover:bg-red-600');
                 purposeSelect.disabled = false;
                 addPurposeBtn.disabled = false;
                 return;
            }
        }

        currentMimeType = mediaRecorder.mimeType || 'audio/webm';
        console.log("Using MIME type:", currentMimeType);

        mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                audioChunks.push(event.data);
            }
        };

        mediaRecorder.onstop = () => {
            const audioBlob = new Blob(audioChunks, { type: currentMimeType });
            uploadAudio(audioBlob, currentMimeType, currentPurposeId);
            audioChunks = [];

            if (drawVisual) cancelAnimationFrame(drawVisual);
            clearInterval(timerInterval);
            timerDisplay.textContent = '00:00';
            canvasCtx.fillStyle = '#e5e7eb';
            canvasCtx.fillRect(0, 0, visualizerCanvas.width, visualizerCanvas.height);

            isRecording = false;
            recordButton.textContent = 'Start Recording';
            recordButton.classList.remove('bg-yellow-500', 'hover:bg-yellow-600');
            recordButton.classList.add('bg-red-500', 'hover:bg-red-600');
            recordButton.disabled = false;
            purposeSelect.disabled = false;
            addPurposeBtn.disabled = false;

            if (source) source.disconnect();
            if (analyser) analyser.disconnect();

            stream.getTracks().forEach(track => track.stop());
        };

        if (!audioContext) {
            
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }

        analyser = audioContext.createAnalyser();
        analyser.fftSize = 256; 
        const bufferLength = analyser.frequencyBinCount;
        dataArray = new Uint8Array(bufferLength);

        source = audioContext.createMediaStreamSource(stream);
        source.connect(analyser);

        audioChunks = [];
        mediaRecorder.start();
        recordingStartTime = Date.now();
        timerInterval = setInterval(updateTimer, 1000);
        draw();
        recordButton.disabled = false;

    } catch (err) {
        console.error("Error accessing microphone:", err);
        showStatus(`Could not access microphone: ${err.message}. Please grant permission.`, true);
        isRecording = false; 
        recordButton.textContent = 'Start Recording';
        recordButton.disabled = (currentPurposeId <= 0); 
        purposeSelect.disabled = false;
        addPurposeBtn.disabled = false;
    }
}

function stopRecording() {
    if (mediaRecorder && mediaRecorder.state === "recording") {
        mediaRecorder.stop();
        recordButton.disabled = true;
        recordButton.textContent = 'Processing...';
    }
}


function openPurposeModal() {
    newPurposeNameInput.value = '';
    newPurposeDescriptionInput.value = '';
    addPurposeModal.style.display = 'block';
    newPurposeNameInput.focus();
}

function closePurposeModal() {
    addPurposeModal.style.display = 'none';
}



recordButton.addEventListener('click', () => {
    if (isRecording) {
        stopRecording();
    } else {
        startRecording();
    }
});

purposeSelect.addEventListener('change', updatePurposeSelection);

sendChatButton.addEventListener('click', () => {
    sendChatMessage(currentPurposeId, chatInput.value);
});

chatInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        sendChatMessage(currentPurposeId, chatInput.value);
    }
});


addPurposeBtn.addEventListener('click', openPurposeModal);
closeModalBtn.addEventListener('click', closePurposeModal);
cancelModalBtn.addEventListener('click', closePurposeModal);
savePurposeBtn.addEventListener('click', () => {
    const name = newPurposeNameInput.value.trim();
    const description = newPurposeDescriptionInput.value.trim();
    if (name) {
        createPurpose(name, description);
    } else {
        showStatus("Purpose name cannot be empty.", true);
        newPurposeNameInput.focus();
    }
});
window.addEventListener('click', (event) => {
    if (event.target == addPurposeModal) {
        closePurposeModal();
    }
});


document.addEventListener('DOMContentLoaded', () => {
    
    const checkAuthentication = async () => {
        try {
            // Try to make an authenticated request to check if we have a valid session
            const response = await fetch(`${API_BASE_URL}/api/purposes`);
            if (response.ok) {
                return true;
            } else if (response.status === 401) {
                showGoogleSignInOverlay();
                return false;
            } else {
                console.error('Unexpected response status:', response.status);
                showGoogleSignInOverlay();
                return false;
            }
        } catch (error) {
            console.error('Error checking authentication:', error);
            showGoogleSignInOverlay();
            return false;
        }
    };

    
    const showGoogleSignInOverlay = (errorMessage = null) => {

        const existingOverlay = document.getElementById('signInOverlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }


        const overlay = document.createElement('div');
        overlay.id = 'signInOverlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';

        let errorHtml = '';
        if (errorMessage) {
            errorHtml = `<div class="bg-red-100 text-red-700 p-3 rounded mb-4">${errorMessage}</div>`;
        }

        overlay.innerHTML = `
            <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
                <h2 class="text-xl font-bold mb-4">Sign In Required</h2>
                ${errorHtml}
                <p class="mb-4">Please sign in to continue:</p>

                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="rememberMeCheckbox" class="mr-2">
                        <span class="text-sm text-gray-700">Remember me for 30 days</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-1">Otherwise, you'll be signed out after 30 hours</p>
                </div>

                <button id="googleSignInBtn" class="w-full px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center gap-2">
                    <svg class="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Sign in with Google
                </button>
            </div>
        `;
        document.body.appendChild(overlay);

        document.getElementById('googleSignInBtn').addEventListener('click', () => {
            const rememberMe = document.getElementById('rememberMeCheckbox').checked;
            initiateGoogleSignIn(rememberMe);
        });
    };

    const initiateGoogleSignIn = (rememberMe) => {
        // Store remember me preference for the callback
        sessionStorage.setItem('rememberMe', rememberMe.toString());

        // Redirect to Google OAuth
        const authUrl = `${API_BASE_URL}/auth/google?remember=${rememberMe}`;
        window.location.href = authUrl;
    };

    const initializeApp = () => {
        fetchPurposes();
        recordButton.disabled = true;
        chatSection.classList.add('hidden');
        chatInput.disabled = true;
        sendChatButton.disabled = true;
        visualizerCanvas.width = visualizerCanvas.offsetWidth;
        visualizerCanvas.height = visualizerCanvas.offsetHeight;
        canvasCtx.fillStyle = '#e5e7eb';
        canvasCtx.fillRect(0, 0, visualizerCanvas.width, visualizerCanvas.height);
    };

    checkAuthentication().then(isAuthenticated => {
        if (isAuthenticated) {
            initializeApp();
        }
    });

    const logoutButton = document.getElementById('logoutButton');
    logoutButton.addEventListener('click', async () => {
        try {
            await fetch(`${API_BASE_URL}/auth/logout`, {
                method: 'POST'
            });
        } catch (error) {
            console.error('Error during logout:', error);
        }
        // Redirect to home page to trigger re-authentication
        window.location.href = '/';
    });
});

window.addEventListener('resize', () => {
    visualizerCanvas.width = visualizerCanvas.offsetWidth;
    visualizerCanvas.height = visualizerCanvas.offsetHeight;
    if (!isRecording) {
        canvasCtx.fillStyle = '#e5e7eb';
        canvasCtx.fillRect(0, 0, visualizerCanvas.width, visualizerCanvas.height);
    }
});


