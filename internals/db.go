package internals

import (
	"database/sql"
	"fmt"
	"log"
	"strings"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver
)

func initDB(dbType, host, port, user, password, dbName, sslMode string) (*sql.DB, error) {
	var dbConn *sql.DB
	var err error

	// Build connection string based on database type
	if dbType == "postgres" {
		connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
			host, port, user, password, dbName, sslMode)
		dbConn, err = sql.Open("postgres", connStr)
		if err != nil {
			return nil, fmt.Errorf("error opening PostgreSQL database: %w", err)
		}
	} else {
		return nil, fmt.Errorf("unsupported database type: %s", dbType)
	}

	// Test connection
	if err = dbConn.Ping(); err != nil {
		dbConn.Close()
		return nil, fmt.<PERSON>rrorf("error connecting to database: %w", err)
	}
	createUsersTableSQL := `CREATE TABLE IF NOT EXISTS users (
		id SERIAL PRIMARY KEY,
		email VARCHAR(255) NOT NULL UNIQUE,
		nickname VARCHAR(255) NOT NULL UNIQUE,
		firstname VARCHAR(255),
		lastname VARCHAR(255),
		age INTEGER,
		gender VARCHAR(50),
		avatar TEXT,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
	);`
	if _, err = dbConn.Exec(createUsersTableSQL); err != nil {
		return nil, fmt.Errorf("error creating users table: %w", err)
	}

	createPurposeTableSQL := `CREATE TABLE IF NOT EXISTS purposes (
		id SERIAL PRIMARY KEY,
		name VARCHAR(255) NOT NULL,
		description TEXT,
		user_id INTEGER,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
		UNIQUE(name, user_id)
	);`
	if _, err = dbConn.Exec(createPurposeTableSQL); err != nil {
		dbConn.Close()
		return nil, fmt.Errorf("error creating purposes table: %w", err)
	}

	createEntriesTableSQL := `CREATE TABLE IF NOT EXISTS entries (
		id SERIAL PRIMARY KEY,
		timestamp TIMESTAMP NOT NULL,
		transcription TEXT NOT NULL,
		summary TEXT,
		user_id INTEGER NOT NULL,
		audio_filename VARCHAR(255) NOT NULL UNIQUE,
		purpose_id INTEGER NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
		FOREIGN KEY (purpose_id) REFERENCES purposes(id) ON DELETE CASCADE
	);`
	if _, err = dbConn.Exec(createEntriesTableSQL); err != nil {
		dbConn.Close()
		return nil, fmt.Errorf("error creating entries table: %w", err)
	}

	createChatHistoryTableSQL := `CREATE TABLE IF NOT EXISTS chat_history (
		id SERIAL PRIMARY KEY,
		purpose_id INTEGER NOT NULL,
		user_id INTEGER NOT NULL,
		timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		role VARCHAR(50) NOT NULL CHECK(role IN ('user', 'model')),
		content TEXT NOT NULL,
		FOREIGN KEY (purpose_id) REFERENCES purposes(id) ON DELETE CASCADE,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
	);`
	if _, err = dbConn.Exec(createChatHistoryTableSQL); err != nil {
		dbConn.Close()
		return nil, fmt.Errorf("error creating chat_history table: %w", err)
	}

	createSessionsTableSQL := `CREATE TABLE IF NOT EXISTS sessions (
		id VARCHAR(255) PRIMARY KEY,
		user_id INTEGER NOT NULL,
		username VARCHAR(255) NOT NULL,
		ip_address VARCHAR(255) NOT NULL,
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		expires_at TIMESTAMP NOT NULL,
		last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
	);`
	if _, err = dbConn.Exec(createSessionsTableSQL); err != nil {
		dbConn.Close()
		return nil, fmt.Errorf("error creating sessions table: %w", err)
	}

	if err = initDefaultPurposes(dbConn); err != nil {
		log.Printf("Warning: Failed to initialize default purposes: %v", err)
	}

	return dbConn, nil
}

// DropAllTables drops all database tables - DANGEROUS, for development only
func DropAllTables() error {
	if db == nil {
		return fmt.Errorf("database connection not initialized")
	}

	log.Println("🗑️  Starting to drop all tables...")

	// List of tables to drop in order (respecting foreign key constraints)
	tables := []string{
		"chat_history",
		"entries",
		"sessions",
		"purposes",
		"users",
	}

	// Drop tables in order
	for _, table := range tables {
		dropSQL := fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE;", table)
		log.Printf("Dropping table: %s", table)

		if _, err := db.Exec(dropSQL); err != nil {
			return fmt.Errorf("error dropping table %s: %w", table, err)
		}
		log.Printf("✅ Dropped table: %s", table)
	}

	log.Println("🎉 All tables dropped successfully!")
	return nil
}

func initDefaultPurposes(dbConn *sql.DB) error {
	var count int
	query := `SELECT COUNT(*) FROM purposes WHERE user_id IS NULL;`
	err := dbConn.QueryRow(query).Scan(&count)
	if err != nil {
		return fmt.Errorf("error checking existing default purposes: %w", err)
	}

	if count > 0 {
		log.Printf("%d default purposes already exist, skipping initialization", count)
		listExistingPurposes(dbConn)
		return nil
	}

	defaultPurposes := []struct {
		name        string
		description string
	}{
		{"Learning", "Journal entries related to educational experiences and new knowledge"},
		{"Mindfulness", "Reflections on meditation, presence, and awareness"},
		{"Work", "Professional experiences, challenges, and achievements"},
		{"Personal Growth", "Self-improvement, goals, and personal development"},
		{"Gratitude", "Things I'm thankful for and positive experiences"},
	}

	insertSQL := `INSERT INTO purposes (name, description, user_id) VALUES ($1, $2, NULL);`
	for _, p := range defaultPurposes {
		_, err := dbConn.Exec(insertSQL, p.name, p.description)
		if err != nil {
			return fmt.Errorf("error inserting default purpose '%s': %w", p.name, err)
		}
		log.Printf("Added default purpose: %s", p.name)
	}

	log.Printf("Successfully added %d default purposes", len(defaultPurposes))
	listExistingPurposes(dbConn)
	return nil
}

func listExistingPurposes(dbConn *sql.DB) {
	rows, err := dbConn.Query("SELECT id, name FROM purposes ORDER BY id;")
	if err != nil {
		log.Printf("Error querying purposes for listing: %v", err)
		return
	}
	defer rows.Close()

	log.Println("Existing purposes in database:")
	for rows.Next() {
		var id int
		var name string
		if err := rows.Scan(&id, &name); err != nil {
			log.Printf("Error scanning purpose row: %v", err)
			continue
		}
		log.Printf("  Purpose ID: %d, Name: %s", id, name)
	}
}

func createPurposeInDB(name string, description *string, userID int) (Purpose, error) {
	var newPurpose Purpose
	query := `INSERT INTO purposes (name, description, user_id) VALUES ($1, $2, $3) RETURNING id, name, description, user_id;`
	err := db.QueryRow(query, name, description, userID).Scan(&newPurpose.ID, &newPurpose.Name, &newPurpose.Description, &newPurpose.UserID)
	if err != nil {
		if strings.Contains(err.Error(), "duplicate key value") || strings.Contains(err.Error(), "UNIQUE constraint failed") {
			return Purpose{}, fmt.Errorf("purpose with name '%s' already exists for this user", name)
		}
		return Purpose{}, fmt.Errorf("error inserting new purpose '%s': %w", name, err)
	}
	log.Printf("Created purpose '%s' with ID: %d for user %d", newPurpose.Name, newPurpose.ID, userID)
	return newPurpose, nil
}

func getAllPurposes(userID int) ([]Purpose, error) {
	// Get all default purposes (user_id IS NULL) and user-specific purposes
	query := `SELECT id, name, description, user_id FROM purposes
			  WHERE user_id IS NULL OR user_id = $1
			  ORDER BY user_id IS NULL DESC, name;`
	rows, err := db.Query(query, userID)
	if err != nil {
		return nil, fmt.Errorf("error querying purposes: %w", err)
	}
	defer rows.Close()
	var purposes []Purpose
	for rows.Next() {
		var p Purpose
		if err := rows.Scan(&p.ID, &p.Name, &p.Description, &p.UserID); err != nil {
			log.Printf("Error scanning purpose row: %v", err)
			continue
		}
		purposes = append(purposes, p)
	}
	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating purpose rows: %w", err)
	}
	return purposes, nil
}

func checkPurposeExists(purposeId int) (bool, error) {
	var count int
	query := `SELECT COUNT(*) FROM purposes WHERE id = $1;`
	err := db.QueryRow(query, purposeId).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("error checking if purpose exists: %w", err)
	}
	return count > 0, nil
}

func checkPurposeAccessible(purposeId int, userID int) (bool, error) {
	var count int
	query := `SELECT COUNT(*) FROM purposes WHERE id = $1 AND (user_id IS NULL OR user_id = $2);`
	err := db.QueryRow(query, purposeId, userID).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("error checking if purpose is accessible: %w", err)
	}
	return count > 0, nil
}

func saveEntryToDB(entry *Entry) (int, error) {
	if entry.PurposeID <= 0 {
		return 0, fmt.Errorf("invalid purpose_id (<= 0) provided for entry")
	}

	exists, err := checkPurposeExists(entry.PurposeID)
	if err != nil {
		return 0, fmt.Errorf("error checking purpose existence: %w", err)
	}
	if !exists {
		return 0, fmt.Errorf("error inserting entry: purpose_id %d does not exist", entry.PurposeID)
	}

	query := `INSERT INTO entries (timestamp, transcription, summary, audio_filename, purpose_id, user_id) VALUES ($1, $2, $3, $4, $5, $6) RETURNING id;`
	var id int
	err = db.QueryRow(query, entry.Timestamp, entry.Transcription, entry.Summary, entry.AudioFilename, entry.PurposeID, entry.UserID).Scan(&id)
	if err != nil {
		return 0, fmt.Errorf("error inserting entry: %w", err)
	}
	return id, nil
}

func getEntries(purposeIdFilter int, userIdFilter int) ([]Entry, error) {
	baseQuery := `SELECT e.id, e.timestamp, e.transcription, e.summary, e.audio_filename, e.created_at, p.id as purpose_id, p.name as purpose_name
				  FROM entries e
				  JOIN purposes p ON e.purpose_id = p.id`

	var conditions []string
	var args []any

	if purposeIdFilter > 0 {
		conditions = append(conditions, fmt.Sprintf("e.purpose_id = $%d", len(args)+1))
		args = append(args, purposeIdFilter)
	}
	if userIdFilter > 0 {
		conditions = append(conditions, fmt.Sprintf("e.user_id = $%d", len(args)+1))
		args = append(args, userIdFilter)
	}

	if len(conditions) > 0 {
		baseQuery += " WHERE " + strings.Join(conditions, " AND ")
	}
	baseQuery += " ORDER BY e.created_at DESC;"

	log.Printf("Querying entries with filters - purpose: %d, user: %d", purposeIdFilter, userIdFilter)
	rows, err := db.Query(baseQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("error querying entries (purpose filter: %d, user filter: %d): %w", purposeIdFilter, userIdFilter, err)
	}
	defer rows.Close()

	var entries []Entry
	for rows.Next() {
		var entry Entry
		var timestampStr, createdAtStr string
		var purposeId int
		var purposeName string
		err := rows.Scan(&entry.ID, &timestampStr, &entry.Transcription, &entry.Summary, &entry.AudioFilename, &createdAtStr, &purposeId, &purposeName)
		if err != nil {
			log.Printf("Error scanning entry row: %v", err)
			continue
		}
		entry.Timestamp, _ = time.Parse(time.RFC3339, timestampStr)
		entry.CreatedAt, _ = time.Parse("2006-01-02 15:04:05", createdAtStr)
		entry.PurposeID = purposeId
		entry.PurposeName = purposeName
		entries = append(entries, entry)
	}
	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating entry rows: %w", err)
	}
	return entries, nil
}

func getChatHistory(purposeId int, userId int, limit int) ([]ChatMessage, error) {
	query := `SELECT id, purpose_id, timestamp, role, content FROM chat_history
			  WHERE purpose_id = $1 AND user_id = $2
			  ORDER BY timestamp DESC LIMIT $3;`
	rows, err := db.Query(query, purposeId, userId, limit)
	if err != nil {
		return nil, fmt.Errorf("error querying chat history for purpose %d and user %d: %w", purposeId, userId, err)
	}
	defer rows.Close()
	var history []ChatMessage
	for rows.Next() {
		var msg ChatMessage
		var timestampStr string
		err := rows.Scan(&msg.ID, &msg.PurposeID, &timestampStr, &msg.Role, &msg.Content)
		if err != nil {
			log.Printf("Error scanning chat history row: %v", err)
			continue
		}
		msg.Timestamp, err = time.Parse("2006-01-02 15:04:05", timestampStr)
		if err != nil {
			log.Printf("Error parsing chat history timestamp '%s': %v", timestampStr, err)
			msg.Timestamp = time.Time{}
		}
		history = append(history, msg)
	}
	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating chat history rows for purpose %d and user %d: %w", purposeId, userId, err)
	}

	for i, j := 0, len(history)-1; i < j; i, j = i+1, j-1 {
		history[i], history[j] = history[j], history[i]
	}
	log.Printf("Retrieved %d chat history messages for purpose %d and user %d", len(history), purposeId, userId)
	return history, nil
}

func saveChatMessage(purposeId int, userId int, role string, content string) error {
	query := `INSERT INTO chat_history (purpose_id, user_id, role, content, timestamp) VALUES ($1, $2, $3, $4, $5);`
	_, err := db.Exec(query, purposeId, userId, role, content, time.Now().UTC().Format("2006-01-02 15:04:05"))
	if err != nil {
		if strings.Contains(err.Error(), "foreign key constraint") || strings.Contains(err.Error(), "FOREIGN KEY constraint failed") {
			return fmt.Errorf("error saving chat message: purpose_id %d or user_id %d does not exist", purposeId, userId)
		}
		return fmt.Errorf("error inserting chat message for purpose %d and user %d: %w", purposeId, userId, err)
	}
	log.Printf("Saved chat message for purpose %d and user %d (Role: %s)", purposeId, userId, role)
	return nil
}
