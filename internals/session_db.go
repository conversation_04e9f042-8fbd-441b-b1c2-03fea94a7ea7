package internals

import (
	"database/sql"
	"log"
	"time"

	"github.com/gofrs/uuid"
)

// DatabaseSessionStore provides database-backed session management
type DatabaseSessionStore struct {
	db *sql.DB
}

// NewDatabaseSessionStore creates a new database-backed session store
func NewDatabaseSessionStore(database *sql.DB) *DatabaseSessionStore {
	return &DatabaseSessionStore{
		db: database,
	}
}

// CreateSession creates a new session with the specified duration
func (store *DatabaseSessionStore) CreateSession(userID int, username, ipAddress string, duration time.Duration) (*Session, error) {
	sessionID, err := uuid.NewV4()
	if err != nil {
		return nil, err
	}

	now := time.Now()
	expiresAt := now.Add(duration)

	query := `INSERT INTO sessions (id, user_id, username, ip_address, created_at, expires_at, last_activity)
			  VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err = store.db.Exec(query, sessionID.String(), userID, username, ipAddress, now, expiresAt, now)
	if err != nil {
		log.Printf("Error creating session in database: %v", err)
		return nil, err
	}

	session := &Session{
		ID:           sessionID,
		UserID:       userID,
		UserName:     username,
		IPAddress:    ipAddress,
		CreatedAt:    now,
		ExpiresAt:    expiresAt,
		LastActivity: now,
	}

	log.Printf("Created session %s for user %d (%s) with expiry %v", sessionID.String(), userID, username, expiresAt)
	return session, nil
}

// GetSession retrieves a session by ID
func (store *DatabaseSessionStore) GetSession(sessionID uuid.UUID) (*Session, error) {
	log.Printf("Attempting to retrieve session: %s", sessionID.String())

	query := `SELECT id, user_id, username, ip_address, created_at, expires_at, last_activity
			  FROM sessions WHERE id = $1`

	var session Session
	var createdAtStr, expiresAtStr, lastActivityStr string

	err := store.db.QueryRow(query, sessionID.String()).Scan(
		&session.ID, &session.UserID, &session.UserName, &session.IPAddress,
		&createdAtStr, &expiresAtStr, &lastActivityStr,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("Session %s not found in database", sessionID.String())
			return nil, nil
		}
		log.Printf("Error retrieving session %s: %v", sessionID.String(), err)
		return nil, err
	}

	log.Printf("Raw timestamp strings from DB - CreatedAt: %s, ExpiresAt: %s, LastActivity: %s",
		createdAtStr, expiresAtStr, lastActivityStr)

	session.CreatedAt, _ = time.Parse(time.RFC3339, createdAtStr)
	session.ExpiresAt, _ = time.Parse(time.RFC3339, expiresAtStr)
	session.LastActivity, _ = time.Parse(time.RFC3339, lastActivityStr)

	log.Printf("Parsed timestamps - CreatedAt: %v, ExpiresAt: %v, LastActivity: %v",
		session.CreatedAt, session.ExpiresAt, session.LastActivity)

	log.Printf("Session found: ID=%s, UserID=%d, ExpiresAt=%v", sessionID.String(), session.UserID, session.ExpiresAt)

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		log.Printf("Session %s is expired, deleting", sessionID.String())
		// Delete expired session
		store.DeleteSession(sessionID)
		return nil, nil
	}

	log.Printf("Session %s is valid, returning", sessionID.String())
	return &session, nil
}

// DeleteSession removes a session from the database
func (store *DatabaseSessionStore) DeleteSession(sessionID uuid.UUID) error {
	query := `DELETE FROM sessions WHERE id = $1`
	_, err := store.db.Exec(query, sessionID.String())
	if err != nil {
		log.Printf("Error deleting session %s: %v", sessionID.String(), err)
		return err
	}
	log.Printf("Deleted session %s", sessionID.String())
	return nil
}

// UpdateLastActivity updates the last activity timestamp for a session
func (store *DatabaseSessionStore) UpdateLastActivity(sessionID uuid.UUID) error {
	query := `UPDATE sessions SET last_activity = $1 WHERE id = $2`
	_, err := store.db.Exec(query, time.Now(), sessionID.String())
	if err != nil {
		log.Printf("Error updating last activity for session %s: %v", sessionID.String(), err)
		return err
	}
	return nil
}

// CleanupExpiredSessions removes all expired sessions from the database
func (store *DatabaseSessionStore) CleanupExpiredSessions() error {
	query := `DELETE FROM sessions WHERE expires_at < $1`
	result, err := store.db.Exec(query, time.Now())
	if err != nil {
		log.Printf("Error cleaning up expired sessions: %v", err)
		return err
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected > 0 {
		log.Printf("Cleaned up %d expired sessions", rowsAffected)
	}
	return nil
}

// GetSessionByUserID retrieves the most recent active session for a user
func (store *DatabaseSessionStore) GetSessionByUserID(userID int) (*Session, error) {
	query := `SELECT id, user_id, username, ip_address, created_at, expires_at, last_activity
			  FROM sessions WHERE user_id = $1 AND expires_at > $2
			  ORDER BY last_activity DESC LIMIT 1`

	var session Session
	var createdAtStr, expiresAtStr, lastActivityStr string

	err := store.db.QueryRow(query, userID, time.Now()).Scan(
		&session.ID, &session.UserID, &session.UserName, &session.IPAddress,
		&createdAtStr, &expiresAtStr, &lastActivityStr,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // No active session found
		}
		log.Printf("Error retrieving session for user %d: %v", userID, err)
		return nil, err
	}

	// Parse timestamps
	session.CreatedAt, _ = time.Parse(time.RFC3339, createdAtStr)
	session.ExpiresAt, _ = time.Parse(time.RFC3339, expiresAtStr)
	session.LastActivity, _ = time.Parse(time.RFC3339, lastActivityStr)

	return &session, nil
}
