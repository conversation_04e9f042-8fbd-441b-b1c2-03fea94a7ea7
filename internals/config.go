package internals

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
)

// Dbconfig holds the database configuration
var Dbconfig *DBConfig

// InitConfig initializes all configuration from environment variables
func InitConfig() error {
	// Load .env file
	err := godotenv.Load()
	if err != nil {
		log.Println("Warning: Error loading .env file:", err)
	} else {
		log.Println("Successfully loaded .env file")
	}

	// Load configuration values
	audioDir = GetEnv("AUDIO_DIR", defaultAudioDir)
	geminiAPIKey = GetEnv("GEMINI_API_KEY", "")
	geminiModelName = GetEnv("GEMINI_MODEL", defaultGeminiModel)
	summaryPrompt = GetEnv("SUMMARY_PROMPT", defaultSummaryPrompt)
	transcriptionPrompt = GetEnv("TRANSCRIPTION_PROMPT", defaultTranscriptionPrompt)

	// Get database configuration

	Dbconfig = &DBConfig{
		Type:     GetEnv("DB_TYPE", "postgres"),
		Host:     GetEnv("DB_HOST", "localhost"),
		Port:     GetEnv("DB_PORT", "5432"),
		User:     GetEnv("DB_USER", "postgres"),
		Password: GetEnv("DB_PASSWORD", ""),
		Name:     GetEnv("DB_NAME", "journal"),
		SSLMode:  GetEnv("DB_SSL_MODE", "disable"),
	}

	maxUploadSizeMB = GetEnvInt("MAX_UPLOAD_SIZE_MB", defaultMaxUploadSizeMB)
	maxEntriesForChatContext = GetEnvInt("MAX_ENTRIES_FOR_CHAT_CONTEXT", defaultMaxEntriesForChat)
	maxHistoryTurnsForContext = GetEnvInt("MAX_HISTORY_TURNS_FOR_CONTEXT", defaultMaxHistoryTurns)
	maxHistoryMessagesToFetch = GetEnvInt("MAX_HISTORY_MESSAGES_TO_FETCH", defaultMaxHistoryMsgs)

	// Validate Gemini API key
	if geminiAPIKey == "" {
		log.Println("Warning: GEMINI_API_KEY not set in environment or .env file")
	}

	// Create audio directory if it doesn't exist
	if err = os.MkdirAll(audioDir, 0o755); err != nil {
		log.Fatalf("Error creating audio directory '%s': %v", audioDir, err)
	}
	log.Printf("Audio files will be saved in: %s", audioDir)

	return nil
}

// InitDatabase initializes the database connection
func InitDatabase(dbType, dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode string) error {
	// Initialize database
	dbConn, err := initDB(dbType, dbHost, dbPort, dbUser, dbPassword, dbName, dbSSLMode)
	if err != nil {
		return fmt.Errorf("error initializing database: %w", err)
	}

	// Set the global database connection
	SetDB(dbConn)

	// Initialize the database session store
	store = NewDatabaseSessionStore(dbConn)
	log.Printf("Database initialized: %s@%s:%s/%s", dbUser, dbHost, dbPort, dbName)

	return nil
}

// GetDefaultServerPort returns the default server port
func GetDefaultServerPort() string {
	return defaultServerPort
}

// GetDefaultDBFile returns the default database file path
func GetDefaultDBFile() string {
	return defaultDBFile
}
