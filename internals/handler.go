package internals

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	sessionuuid "github.com/gofrs/uuid"
	"github.com/google/uuid"
)

// HandleSaveEntry handles saving a new journal entry
func HandleSaveEntry(w http.ResponseWriter, r *http.Request) {
	log.Println("Received request to save entry (v6)")
	r.Body = http.MaxBytesReader(w, r.Body, int64(maxUploadSizeMB*1024*1024))
	err := r.ParseMultipartForm(int64(maxUploadSizeMB * 1024 * 1024))
	if err != nil {
		log.Printf("Error parsing multipart form: %v", err)
		http.Error(w, fmt.Sprintf("Error parsing form data: %v", err), http.StatusBadRequest)
		return
	}

	file, header, err := r.FormFile("audio")
	if err != nil {
		log.Printf("Error retrieving 'audio' file: %v", err)
		http.Error(w, "Error retrieving audio file", http.StatusBadRequest)
		return
	}
	defer file.Close()
	mimeType := r.FormValue("mimeType")
	if mimeType == "" {
		log.Println("Error: Missing 'mimeType'")
		http.Error(w, "Missing 'mimeType' field for audio", http.StatusBadRequest)
		return
	}
	log.Printf("Received audio: %s, Size: %d, MIME: %s", header.Filename, header.Size, mimeType)
	if header.Size == 0 {
		http.Error(w, "Received empty audio file", http.StatusBadRequest)
		return
	}

	purposeIdStr := r.FormValue("purposeId")
	purposeId, err := strconv.Atoi(purposeIdStr)
	if err != nil || purposeId <= 0 {
		log.Printf("Invalid or missing 'purposeId' field: '%s', Error: %v", purposeIdStr, err)
		http.Error(w, "Missing or invalid 'purposeId' field (must be a positive integer)", http.StatusBadRequest)
		return
	}
	log.Printf("Received purpose ID: %d", purposeId)

	userId, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	accessible, err := checkPurposeAccessible(purposeId, userId)
	if err != nil {
		log.Printf("Error checking if purpose is accessible: %v", err)
		http.Error(w, "Error validating purpose", http.StatusInternalServerError)
		return
	}
	if !accessible {
		log.Printf("Purpose with ID %d is not accessible to user %d", purposeId, userId)
		http.Error(w, fmt.Sprintf("Purpose with ID %d is not accessible", purposeId), http.StatusForbidden)
		return
	}

	audioData, err := io.ReadAll(file)
	if err != nil {
		log.Printf("Error reading audio data: %v", err)
		http.Error(w, "Error processing audio file", http.StatusInternalServerError)
		return
	}

	fileExt := filepath.Ext(header.Filename)
	if fileExt == "" {
		switch {
		case strings.Contains(mimeType, "webm"):
			fileExt = ".webm"
		case strings.Contains(mimeType, "ogg"):
			fileExt = ".ogg"
		case strings.Contains(mimeType, "wav"):
			fileExt = ".wav"
		case strings.Contains(mimeType, "mp3"):
			fileExt = ".mp3"
		case strings.Contains(mimeType, "mpeg"):
			fileExt = ".mp3"
		default:
			fileExt = ".audio"
		}
	}
	uniqueFilename := fmt.Sprintf("%s%s", uuid.New().String(), fileExt)
	filePath := filepath.Join(audioDir, uniqueFilename)
	err = os.WriteFile(filePath, audioData, 0o644)
	if err != nil {
		log.Printf("Error saving audio file '%s': %v", filePath, err)
		http.Error(w, "Error saving audio file", http.StatusInternalServerError)
		return
	}
	log.Printf("Audio file saved: %s", filePath)

	var transcription, summary string
	var transcriptionErr, summaryErr error

	transcription, transcriptionErr = transcribeAudioWithGemini(audioData, mimeType)
	if transcriptionErr != nil {
		log.Printf("Error generating transcription: %v", transcriptionErr)
		os.Remove(filePath)
		http.Error(w, fmt.Sprintf("Failed to transcribe audio: %v", transcriptionErr), http.StatusInternalServerError)
		return
	}

	summary, summaryErr = generateGeminiSummary(transcription)
	if summaryErr != nil {
		log.Printf("Error generating summary: %v", summaryErr)
		summary = "Error generating summary."
	}

	entryUserId, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		os.Remove(filePath)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	entry := &Entry{
		Timestamp:     time.Now().UTC(),
		Transcription: transcription,
		Summary:       summary,
		AudioFilename: uniqueFilename,
		PurposeID:     purposeId,
		UserID:        entryUserId,
	}

	entryId, err := saveEntryToDB(entry)
	if err != nil {
		log.Printf("Error saving entry to DB: %v", err)
		os.Remove(filePath)
		http.Error(w, "Error saving entry data", http.StatusInternalServerError)
		return
	}
	entry.ID = entryId
	entry.Timestamp = entry.Timestamp.Local()
	var pName string
	db.QueryRow("SELECT name FROM purposes WHERE id = $1", purposeId).Scan(&pName)
	entry.PurposeName = pName

	log.Printf("Entry saved: ID=%d, PurposeID=%d", entryId, purposeId)
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(map[string]any{"message": "Entry saved successfully", "entry": entry})
}

func getUserFromRequest(r *http.Request) (int, error) {
	// Get session cookie
	sessionCookie, err := r.Cookie("session")
	if err != nil {
		log.Printf("Request missing session cookie: %s", r.URL.Path)
		return 0, fmt.Errorf("missing authentication")
	}

	if sessionCookie.Value == "" {
		log.Printf("Request has empty session cookie: %s", r.URL.Path)
		return 0, fmt.Errorf("missing authentication")
	}

	sessionID, err := sessionuuid.FromString(sessionCookie.Value)
	if err != nil {
		log.Printf("Invalid session ID format: %v", err)
		return 0, fmt.Errorf("invalid session")
	}

	session, err := store.GetSession(sessionID)
	if err != nil {
		log.Printf("Error retrieving session: %v", err)
		return 0, fmt.Errorf("session error")
	}

	if session == nil {
		log.Printf("Session not found or expired: %s", r.URL.Path)
		return 0, fmt.Errorf("session expired")
	}

	// Update last activity
	store.UpdateLastActivity(sessionID)
	return session.UserID, nil
}

// HandleLogout handles user logout by clearing the session
func HandleLogout(w http.ResponseWriter, r *http.Request) {
	sessionCookie, err := r.Cookie("session")
	if err == nil && sessionCookie.Value != "" {
		sessionID, err := sessionuuid.FromString(sessionCookie.Value)
		if err == nil {
			// Delete session from store
			store.DeleteSession(sessionID)
		}
	}

	// Clear the session cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "session",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		MaxAge:   -1, // Delete the cookie
	})

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "Logged out successfully"})
}

// HandleGetUserProfile returns the current user's profile information
func HandleGetUserProfile(w http.ResponseWriter, r *http.Request) {
	userId, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Get user information from database
	var user User
	err = db.QueryRow("SELECT id, email, nickname, avatar, created_at FROM users WHERE id = $1", userId).Scan(
		&user.ID, &user.Email, &user.NickName, &user.Avatar, &user.CreatedAt)
	if err != nil {
		log.Printf("Error retrieving user profile: %v", err)
		http.Error(w, "Error retrieving user profile", http.StatusInternalServerError)
		return
	}

	// Get active sessions count
	var sessionCount int
	err = db.QueryRow("SELECT COUNT(*) FROM sessions WHERE user_id = $1 AND expires_at > $2", userId, time.Now()).Scan(&sessionCount)
	if err != nil {
		log.Printf("Error counting user sessions: %v", err)
		sessionCount = 0 // Default to 0 if error
	}

	profileResponse := map[string]any{
		"id":              user.ID,
		"email":           user.Email,
		"nickname":        user.NickName,
		"avatar":          user.Avatar,
		"created_at":      user.CreatedAt,
		"active_sessions": sessionCount,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(profileResponse)
}

// HandleSignOutAllDevices signs out the user from all devices by deleting all their sessions
func HandleSignOutAllDevices(w http.ResponseWriter, r *http.Request) {
	userId, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Delete all sessions for this user
	result, err := db.Exec("DELETE FROM sessions WHERE user_id = $1", userId)
	if err != nil {
		log.Printf("Error deleting user sessions: %v", err)
		http.Error(w, "Error signing out from all devices", http.StatusInternalServerError)
		return
	}

	rowsAffected, _ := result.RowsAffected()
	log.Printf("Signed out user %d from %d devices", userId, rowsAffected)

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]any{
		"message":          "Successfully signed out from all devices",
		"sessions_deleted": rowsAffected,
	})
}

// HandleGetEntries handles retrieving journal entries
func HandleGetEntries(w http.ResponseWriter, r *http.Request) {
	userId, erra := getUserFromRequest(r)
	if erra != nil {
		log.Printf("Error getting user from request: %v", erra)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}
	purposeIdStr := r.URL.Query().Get("purposeId")
	purposeIdFilter := 0
	var err error
	if purposeIdStr != "" {
		purposeIdFilter, err = strconv.Atoi(purposeIdStr)
		if err != nil || purposeIdFilter < 0 {
			log.Printf("Invalid purposeId query parameter: '%s'. Error: %v", purposeIdStr, err)
			http.Error(w, "Invalid purposeId query parameter.", http.StatusBadRequest)
			return
		}
		log.Printf("Received request to get entries for purposeId: %d", purposeIdFilter)
	} else {
		log.Println("Received request to get all entries")
	}
	entries, err := getEntries(purposeIdFilter, userId)
	if err != nil {
		log.Printf("Error retrieving entries: %v", err)
		http.Error(w, "Error retrieving entries", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(entries); err != nil {
		log.Printf("Error encoding entries to JSON: %v", err)
	}
	log.Printf("Sent %d entries (filter: %d)", len(entries), purposeIdFilter)
}

// HandleGetPurposes handles retrieving all purposes for a user
func HandleGetPurposes(w http.ResponseWriter, r *http.Request) {
	log.Println("Received request to get purposes")

	userId, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	purposes, err := getAllPurposes(userId)
	if err != nil {
		log.Printf("Error retrieving purposes: %v", err)
		http.Error(w, "Error retrieving purposes", http.StatusInternalServerError)
		return
	}

	log.Printf("Purposes being sent to client for user %d:", userId)
	for _, p := range purposes {
		log.Printf("  Purpose ID: %d, Name: %s", p.ID, p.Name)
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(purposes); err != nil {
		log.Printf("Error encoding purposes to JSON: %v", err)
	}
	log.Printf("Sent %d purposes for user %d", len(purposes), userId)
}

// HandleCreatePurpose handles creating a new purpose for a user
func HandleCreatePurpose(w http.ResponseWriter, r *http.Request) {
	log.Println("Received request to create purpose")

	userId, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var req CreatePurposeRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("Error decoding create purpose request body: %v", err)
		http.Error(w, "Invalid request body.", http.StatusBadRequest)
		return
	}

	req.Name = strings.TrimSpace(req.Name)
	if req.Name == "" {
		http.Error(w, "Purpose name cannot be empty.", http.StatusBadRequest)
		return
	}

	var descriptionPtr *string
	if strings.TrimSpace(req.Description) != "" {
		descriptionPtr = &req.Description
	}

	newPurpose, err := createPurposeInDB(req.Name, descriptionPtr, userId)
	if err != nil {
		log.Printf("Error creating purpose in DB: %v", err)

		if strings.Contains(err.Error(), "already exists") {
			http.Error(w, err.Error(), http.StatusConflict)
		} else {
			http.Error(w, "Error creating purpose.", http.StatusInternalServerError)
		}
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	if err := json.NewEncoder(w).Encode(newPurpose); err != nil {
		log.Printf("Error encoding created purpose to JSON: %v", err)
	}
	log.Printf("Purpose '%s' created successfully for user %d.", newPurpose.Name, userId)
}

// HandleChat handles chat requests for a specific purpose
func HandleChat(w http.ResponseWriter, r *http.Request) {
	userid, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}
	purposeIdStr := r.PathValue("purposeId")
	purposeId, err := strconv.Atoi(purposeIdStr)
	if err != nil || purposeId <= 0 {
		log.Printf("Invalid purposeId path parameter: '%s'", purposeIdStr)
		http.Error(w, "Invalid purpose ID in path.", http.StatusBadRequest)
		return
	}
	log.Printf("Received chat request for purposeId: %d", purposeId)

	// Check if user has access to this purpose
	accessible, err := checkPurposeAccessible(purposeId, userid)
	if err != nil {
		log.Printf("Error checking if purpose is accessible: %v", err)
		http.Error(w, "Error validating purpose", http.StatusInternalServerError)
		return
	}
	if !accessible {
		log.Printf("Purpose with ID %d is not accessible to user %d", purposeId, userid)
		http.Error(w, "Purpose not accessible", http.StatusForbidden)
		return
	}

	var req ChatRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("Error decoding chat request body: %v", err)
		http.Error(w, "Invalid request body.", http.StatusBadRequest)
		return
	}
	if strings.TrimSpace(req.Message) == "" {
		http.Error(w, "Message content cannot be empty.", http.StatusBadRequest)
		return
	}
	userMessage := req.Message
	entries, err := getEntries(purposeId, userid)
	if err != nil {
		log.Printf("Error fetching entries for chat context (purpose %d): %v", purposeId, err)
		http.Error(w, "Error retrieving journal entries.", http.StatusInternalServerError)
		return
	}
	purposeName := "Unknown Purpose"
	if len(entries) > 0 {
		purposeName = entries[0].PurposeName
	} else {
		var p Purpose
		query := `SELECT id, name FROM purposes WHERE id = $1;`
		err = db.QueryRow(query, purposeId).Scan(&p.ID, &p.Name)
		if err == nil {
			purposeName = p.Name
		} else if err == sql.ErrNoRows {
			log.Printf("Purpose ID %d not found when fetching name for chat.", purposeId)
			http.Error(w, "Purpose not found.", http.StatusNotFound)
			return
		} else {
			log.Printf("Error fetching purpose name for chat (purpose %d): %v", purposeId, err)
			http.Error(w, "Error retrieving purpose details.", http.StatusInternalServerError)
			return
		}
	}
	history, err := getChatHistory(purposeId, userid, maxHistoryMessagesToFetch)
	if err != nil {
		log.Printf("Error fetching chat history for context (purpose %d, user %d): %v", purposeId, userid, err)
		http.Error(w, "Error retrieving chat history.", http.StatusInternalServerError)
		return
	}
	modelReply, err := generateChatResponse(purposeId, purposeName, entries, history, userMessage)
	if err != nil {
		log.Printf("Error generating Gemini chat response (purpose %d, user %d): %v", purposeId, userid, err)
		http.Error(w, fmt.Sprintf("Error generating response: %v", err), http.StatusInternalServerError)
		return
	}
	err = saveChatMessage(purposeId, userid, "user", userMessage)
	if err != nil {
		log.Printf("Error saving user chat message (purpose %d, user %d): %v", purposeId, userid, err)
	}
	err = saveChatMessage(purposeId, userid, "model", modelReply)
	if err != nil {
		log.Printf("Error saving model chat message (purpose %d, user %d): %v", purposeId, userid, err)
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(ChatResponse{Reply: modelReply})
	log.Printf("Sent chat reply for purpose %d, user %d.", purposeId, userid)
}

// HandleGetChatHistory handles retrieving chat history for a purpose
func HandleGetChatHistory(w http.ResponseWriter, r *http.Request) {
	userId, err := getUserFromRequest(r)
	if err != nil {
		log.Printf("Error getting user from request: %v", err)
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	purposeIdStr := r.PathValue("purposeId")
	purposeId, err := strconv.Atoi(purposeIdStr)
	if err != nil || purposeId <= 0 {
		log.Printf("Invalid purposeId path parameter in GET history: '%s'", purposeIdStr)
		http.Error(w, "Invalid purpose ID in path.", http.StatusBadRequest)
		return
	}
	log.Printf("Received request to get chat history for purposeId: %d, userId: %d", purposeId, userId)

	// Check if user has access to this purpose
	accessible, err := checkPurposeAccessible(purposeId, userId)
	if err != nil {
		log.Printf("Error checking if purpose is accessible: %v", err)
		http.Error(w, "Error validating purpose", http.StatusInternalServerError)
		return
	}
	if !accessible {
		log.Printf("Purpose with ID %d is not accessible to user %d", purposeId, userId)
		http.Error(w, "Purpose not accessible", http.StatusForbidden)
		return
	}

	history, err := getChatHistory(purposeId, userId, maxHistoryMessagesToFetch)
	if err != nil {
		log.Printf("Error fetching chat history for API response (purpose %d, user %d): %v", purposeId, userId, err)
		http.Error(w, "Error retrieving chat history.", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(history); err != nil {
		log.Printf("Error encoding chat history to JSON (purpose %d, user %d): %v", purposeId, userId, err)
	}
	log.Printf("Sent %d chat history messages for purpose %d, user %d", len(history), purposeId, userId)
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// HandleIndexHTML serves the index.html file
func HandleIndexHTML(w http.ResponseWriter, r *http.Request) {
	data, err := os.ReadFile("index.html")
	if err != nil {
		log.Printf("Error reading index.html: %v", err)
		http.Error(w, "Error reading index.html", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "text/html")
	w.Write(data)
}

func HandleHomeHTML(w http.ResponseWriter, r *http.Request) {
	data, err := os.ReadFile("homepage.html")
	if err != nil {
		log.Printf("Error reading homepageno.html: %v", err)
		http.Error(w, "Error reading index.html", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "text/html")
	w.Write(data)
}

// HandleAppJS serves the app.js file
func HandleAppJS(w http.ResponseWriter, r *http.Request) {
	data, err := os.ReadFile("app.js")
	if err != nil {
		log.Printf("Error reading app.js: %v", err)
		http.Error(w, "Error reading app.js", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "application/javascript")
	w.Write(data)
}

// HandlePricingHTML serves the pricing.html file
func HandlePricingHTML(w http.ResponseWriter, r *http.Request) {
	data, err := os.ReadFile("pricing.html")
	if err != nil {
		log.Printf("Error reading pricing.html: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "text/html")
	w.Write(data)
}

// HandleSettings handles the settings page - requires authentication
func HandleSettings(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		// Check if user is authenticated
		_, err := getUserFromRequest(r)
		if err != nil {
			// Redirect to home page if not authenticated
			http.Redirect(w, r, "/", http.StatusSeeOther)
			return
		}

		http.ServeFile(w, r, "settings.html")
	} else {
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

