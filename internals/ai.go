package internals

import (
	"context"
	"fmt"
	"log"
	"sort"
	"strings"
	"time"

	"github.com/google/generative-ai-go/genai"
	"google.golang.org/api/option"
)

func transcribeAudioWithGemini(audioData []byte, mimeType string) (string, error) {
	if geminiAPIKey == "" {
		return "", fmt.Errorf("gemini API key is not configured")
	}
	if len(audioData) == 0 {
		return "", fmt.<PERSON>("cannot transcribe empty audio data")
	}
	if mimeType == "" {
		return "", fmt.Errorf("audio MIME type is required for transcription")
	}
	log.Printf("Sending audio data (Size: %d bytes, MIME: %s) to Gemini for transcription...", len(audioData), mimeType)
	ctx := context.Background()
	client, err := genai.NewClient(ctx, option.WithAPIKey(geminiAPIKey))
	if err != nil {
		return "", fmt.<PERSON>rro<PERSON>("error creating Gemini client: %w", err)
	}
	defer client.Close()
	model := client.GenerativeModel(geminiModelName)
	model.ResponseMIMEType = "text/plain"
	prompt := genai.Text(transcriptionPrompt)
	audioBlob := genai.Blob{MIMEType: mimeType, Data: audioData}
	resp, err := model.GenerateContent(ctx, prompt, audioBlob)
	if err != nil {
		log.Printf("Error generating transcription with Gemini: %v", err)
		return "", fmt.Errorf("error sending transcription request to Gemini API: %w", err)
	}
	var transcription strings.Builder
	if len(resp.Candidates) > 0 && resp.Candidates[0].Content != nil {
		for _, part := range resp.Candidates[0].Content.Parts {
			transcription.WriteString(fmt.Sprintf("%v", part))
		}
	} else {
		finishReason := "unknown"
		if len(resp.Candidates) > 0 {
			finishReason = string(resp.Candidates[0].FinishReason)
		}
		log.Printf("Warning: Received no content or candidates from Gemini transcription response. Finish Reason: %s", finishReason)
		return "", fmt.Errorf("received empty or invalid transcription response from Gemini API (Finish Reason: %s)", finishReason)
	}
	transcribedText := transcription.String()
	if transcribedText == "" {
		log.Println("Warning: Gemini returned an empty transcription.")
	}
	log.Printf("Successfully received transcription from Gemini (Length: %d chars)", len(transcribedText))
	return transcribedText, nil
}

func generateGeminiSummary(transcription string) (string, error) {
	if geminiAPIKey == "" {
		log.Println("Gemini API key not configured. Skipping summary generation.")
		return "Summary generation skipped (API key missing).", nil
	}
	if transcription == "" {
		log.Println("Transcription is empty. Skipping summary generation.")
		return "No transcription to summarize.", nil
	}
	ctx := context.Background()
	client, err := genai.NewClient(ctx, option.WithAPIKey(geminiAPIKey))
	if err != nil {
		return "", fmt.Errorf("error creating Gemini client for summary: %w", err)
	}
	defer client.Close()
	model := client.GenerativeModel(geminiModelName)
	model.ResponseMIMEType = "text/plain"
	fullPrompt := summaryPrompt + transcription
	log.Printf("Sending text to Gemini for summary (first 50 chars): %s...", fullPrompt[:min(50, len(fullPrompt))])
	resp, err := model.GenerateContent(ctx, genai.Text(fullPrompt))
	if err != nil {
		log.Printf("Error generating summary with Gemini: %v", err)
		return "", fmt.Errorf("error sending summary request to Gemini API: %w", err)
	}
	var summary strings.Builder
	if len(resp.Candidates) > 0 && resp.Candidates[0].Content != nil {
		for _, part := range resp.Candidates[0].Content.Parts {
			summary.WriteString(fmt.Sprintf("%v", part))
		}
	} else {
		finishReason := "unknown"
		if len(resp.Candidates) > 0 {
			finishReason = string(resp.Candidates[0].FinishReason)
		}
		log.Println("Warning: Received no content or candidates from Gemini summary response.")
		return "", fmt.Errorf("received empty or invalid summary response from Gemini API (Finish Reason: %s)", finishReason)
	}
	log.Println("Successfully received summary from Gemini.")
	return summary.String(), nil
}

func formatEntriesForPrompt(entries []Entry) string {
	var contextBuilder strings.Builder
	contextBuilder.WriteString("JOURNAL ENTRIES CONTEXT:\n")
	if len(entries) == 0 {
		contextBuilder.WriteString("No journal entries found for this purpose.\n")
	} else {
		entriesToSend := entries
		if len(entries) > maxEntriesForChatContext {
			sort.Slice(entriesToSend, func(i, j int) bool { return entriesToSend[i].CreatedAt.After(entriesToSend[j].CreatedAt) })
			if maxEntriesForChatContext > 0 {
				entriesToSend = entriesToSend[:min(len(entriesToSend), maxEntriesForChatContext)]
				contextBuilder.WriteString(fmt.Sprintf("(Showing latest %d entries)\n", len(entriesToSend)))
				for i, j := 0, len(entriesToSend)-1; i < j; i, j = i+1, j-1 {
					entriesToSend[i], entriesToSend[j] = entriesToSend[j], entriesToSend[i]
				}
			} else {
				entriesToSend = []Entry{}
				contextBuilder.WriteString("(Entry context limit reached, not showing entries)\n")
			}
		} else {
			contextBuilder.WriteString(fmt.Sprintf("(Showing all %d entries)\n", len(entriesToSend)))
			sort.Slice(entriesToSend, func(i, j int) bool { return entriesToSend[i].CreatedAt.Before(entriesToSend[j].CreatedAt) })
		}
		for _, entry := range entriesToSend {
			entryContext := fmt.Sprintf("---\nDate: %s\nSummary: %s\nTranscription: %s\n", entry.Timestamp.Format(time.RFC1123), entry.Summary, entry.Transcription)
			contextBuilder.WriteString(entryContext)
		}
		if len(entriesToSend) > 0 {
			contextBuilder.WriteString("---\n")
		}
	}
	return contextBuilder.String()
}

func generateChatResponse(purposeId int, purposeName string, entries []Entry, history []ChatMessage, userMessage string) (string, error) {
	if geminiAPIKey == "" {
		return "", fmt.Errorf("gemini API key is not configured")
	}
	ctx := context.Background()
	client, err := genai.NewClient(ctx, option.WithAPIKey(geminiAPIKey))
	if err != nil {
		return "", fmt.Errorf("error creating Gemini client for chat: %w", err)
	}
	defer client.Close()
	model := client.GenerativeModel(geminiModelName)
	sessionHistory := []*genai.Content{}
	systemInstruction := fmt.Sprintf("You are a helpful assistant chatting with a user about their voice journal entries for the purpose: '%s'. Relevant journal entries are provided below. Keep your responses concise and relevant.", purposeName)
	entryContextString := formatEntriesForPrompt(entries)
	initialUserContent := systemInstruction + "\n\n" + entryContextString
	sessionHistory = append(sessionHistory, &genai.Content{Parts: []genai.Part{genai.Text(initialUserContent)}, Role: "user"})
	sessionHistory = append(sessionHistory, &genai.Content{Parts: []genai.Part{genai.Text("Okay, I understand the context. How can I help you regarding these entries?")}, Role: "model"})
	for _, msg := range history {
		sessionHistory = append(sessionHistory, &genai.Content{Parts: []genai.Part{genai.Text(msg.Content)}, Role: msg.Role})
	}
	session := model.StartChat()
	if len(sessionHistory) > maxHistoryTurnsForContext*2 {
		startIndex := len(sessionHistory) - maxHistoryTurnsForContext*2
		session.History = sessionHistory[startIndex:]
		log.Printf("Chat history truncated to last %d messages for purpose %d", len(session.History), purposeId)
	} else {
		session.History = sessionHistory
	}
	log.Printf("Sending chat message to Gemini for purpose %d via ChatSession. History turns included: %d", purposeId, len(session.History)/2)
	currentUserContent := &genai.Content{Parts: []genai.Part{genai.Text(userMessage)}}
	resp, err := session.SendMessage(ctx, currentUserContent.Parts...)
	if err != nil {
		log.Printf("Error sending message via ChatSession: %v", err)
		return "", fmt.Errorf("error sending chat message to Gemini API: %w", err)
	}
	var reply strings.Builder
	if len(resp.Candidates) > 0 && resp.Candidates[0].Content != nil {
		for _, part := range resp.Candidates[0].Content.Parts {
			reply.WriteString(fmt.Sprintf("%v", part))
		}
	} else {
		finishReason := "unknown"
		if len(resp.Candidates) > 0 {
			finishReason = string(resp.Candidates[0].FinishReason)
		}
		log.Printf("Warning: Received no content or candidates from Gemini chat response. Finish Reason: %s", finishReason)
		return "", fmt.Errorf("received empty or invalid chat response from Gemini API (Finish Reason: %s)", finishReason)
	}
	responseText := strings.TrimSpace(reply.String())
	if responseText == "" {
		log.Println("Warning: Gemini returned an empty chat response string.")
	}
	log.Printf("Successfully received chat response from Gemini for purpose %d.", purposeId)
	return responseText, nil
}
