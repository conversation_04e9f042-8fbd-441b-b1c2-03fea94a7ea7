package internals

import (
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
)

// generateStateToken creates a random state token for OAuth flow
func generateStateToken() (string, error) {
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		return "", err
	}
	state := base64.URLEncoding.EncodeToString(b)
	stateTokens[state] = time.Now().Add(15 * time.Minute)
	return state, nil
}

// InitiateGoogleAuth starts the Google OAuth flow
func InitiateGoogleAuth(w http.ResponseWriter, r *http.Request) {
	state, err := generateStateToken()
	if err != nil {
		log.Printf("Error generating state token: %v", err)
		http.Error(w, "Failed to generate state token", http.StatusInternalServerError)
		return
	}

	// Include remember me parameter in state
	rememberMe := r.URL.Query().Get("remember") == "true"
	stateWithRemember := fmt.Sprintf("%s:%t", state, rememberMe)

	authURL := fmt.Sprintf(
		"https://accounts.google.com/o/oauth2/v2/auth?"+
			"client_id=%s"+
			"&redirect_uri=%s"+
			"&response_type=code"+
			"&scope=email+profile"+
			"&state=%s"+
			"&access_type=offline"+
			"&prompt=consent",
		url.QueryEscape(googleConfig.ClientID),
		url.QueryEscape(googleConfig.RedirectURI),
		url.QueryEscape(stateWithRemember),
	)

	http.Redirect(w, r, authURL, http.StatusTemporaryRedirect)
}

// HandleGoogleCallback processes the callback from Google
func HandleGoogleCallback(w http.ResponseWriter, r *http.Request) {
	// Extract state and code from query parameters
	state := r.URL.Query().Get("state")
	code := r.URL.Query().Get("code")

	// Extract remember me from state
	stateParts := strings.Split(state, ":")
	var actualState string
	var rememberMe bool
	if len(stateParts) == 2 {
		actualState = stateParts[0]
		rememberMe = stateParts[1] == "true"
	} else {
		actualState = state
		rememberMe = false
	}

	// Validate state token
	expiry, exists := stateTokens[actualState]
	if !exists || time.Now().After(expiry) {
		log.Printf("Invalid state token or token expired")
		http.Error(w, "Invalid state token", http.StatusBadRequest)
		return
	}
	delete(stateTokens, actualState)

	// Exchange code for token
	tokenResponse, err := exchangeCodeForToken(code)
	if err != nil {
		log.Printf("Error exchanging code for token: %v", err)
		http.Error(w, "Failed to exchange code for token", http.StatusInternalServerError)
		return
	}

	log.Printf("Token exchange successful. Access token length: %d, Token type: %s",
		len(tokenResponse.AccessToken), tokenResponse.TokenType)

	// Get user info using the access token
	userInfo, err := getGoogleUserInfo(tokenResponse.AccessToken)
	if err != nil {
		log.Printf("Error getting user info: %v", err)
		http.Error(w, "Failed to get user info", http.StatusInternalServerError)
		return
	}

	// Log the user info received from Google
	log.Printf("Google user info received: ID=%s, Email=%s, Name=%s, GivenName=%s, FamilyName=%s, Picture=%s",
		userInfo.ID, userInfo.Email, userInfo.Name, userInfo.GivenName, userInfo.FamilyName, userInfo.Picture)

	// Check if user exists in our database
	users := ReadfromDb()
	var existingUser *User
	for _, user := range users {
		if user.Email == userInfo.Email {
			existingUser = &user
			break
		}
	}

	var user User
	if existingUser == nil {
		// Get the image from userInfo.Picture
		link := userInfo.Picture
		fmt.Println(link)
		// Create new user
		user = User{
			Email:     userInfo.Email,
			NickName:  generateUsername(userInfo),
			Avatar:    link,
			CreatedAt: time.Now(),
		}

		err = SaveUserToDb(user)
		if err != nil {
			log.Printf("Error saving user to database: %v", err)
			http.Error(w, "Failed to create user", http.StatusInternalServerError)
			return
		}

		users := ReadfromDb()
		for _, item := range users {
			if item.Email == user.Email {
				user.ID = item.ID
				break
			}
		}
	} else {
		user = *existingUser
		if oldsession, err := store.GetSessionByUserID(user.ID); err == nil && oldsession != nil {
			store.DeleteSession(oldsession.ID)
		}
	}

	// Determine session duration based on remember me
	var sessionDuration time.Duration
	var maxAge int
	if rememberMe {
		sessionDuration = 30 * 24 * time.Hour // 30 days
		maxAge = 30 * 24 * 60 * 60            // 30 days in seconds
	} else {
		sessionDuration = 30 * time.Hour // 30 hours
		maxAge = 30 * 60 * 60            // 30 hours in seconds
	}

	// Create session
	session, err := store.CreateSession(user.ID, user.NickName, r.RemoteAddr, sessionDuration)
	if err != nil {
		log.Printf("Error creating session: %v", err)
		http.Error(w, "Failed to create session", http.StatusInternalServerError)
		return
	}
	// Set session cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "session",
		Value:    session.ID.String(),
		Path:     "/",
		HttpOnly: true,
		MaxAge:   maxAge,
	})

	// Redirect to home page
	http.Redirect(w, r, "/app", http.StatusTemporaryRedirect)
}

// exchangeCodeForToken exchanges the authorization code for tokens
func exchangeCodeForToken(code string) (*GoogleTokenResponse, error) {
	data := url.Values{}
	data.Set("code", code)
	data.Set("client_id", googleConfig.ClientID)
	data.Set("client_secret", googleConfig.ClientSecret)
	data.Set("redirect_uri", googleConfig.RedirectURI)
	data.Set("grant_type", "authorization_code")

	log.Printf("Exchanging code for token. Code length: %d", len(code))

	resp, err := http.PostForm("https://oauth2.googleapis.com/token", data)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	log.Printf("Token exchange response status: %d", resp.StatusCode)
	log.Printf("Token exchange response body: %s", string(body))

	if resp.StatusCode != 200 {
		return nil, fmt.Errorf("token exchange failed with status %d: %s", resp.StatusCode, string(body))
	}

	var tokenResponse GoogleTokenResponse
	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return nil, err
	}

	return &tokenResponse, nil
}

// getGoogleUserInfo retrieves the user's information from Google
func getGoogleUserInfo(accessToken string) (*GoogleUserInfo, error) {
	req, err := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v2/userinfo", nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+accessToken)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	log.Printf("Google userinfo response status: %d", resp.StatusCode)
	log.Printf("Google userinfo response body: %s", string(body))

	var userInfo GoogleUserInfo
	if err := json.Unmarshal(body, &userInfo); err != nil {
		log.Printf("Error unmarshaling Google userinfo: %v", err)
		return nil, err
	}

	return &userInfo, nil
}

// generateUsername generates a username based on the user's information
func generateUsername(userInfo *GoogleUserInfo) string {
	var base string

	// Prefer name components if available
	if userInfo.GivenName != "" && userInfo.FamilyName != "" {
		base = userInfo.GivenName + "." + userInfo.FamilyName
	} else if userInfo.Name != "" {
		base = userInfo.Name
	} else {
		base = strings.Split(userInfo.Email, "@")[0]
	}

	// Clean invalid characters using regex
	reg := regexp.MustCompile(`[^a-zA-Z0-9_-]`)
	clean := reg.ReplaceAllString(base, "-")

	// Normalize to title case
	clean = titleCase(clean)

	// Trim length (3-20 characters is common for usernames)
	if len(clean) > 20 {
		clean = clean[:20]
	}

	// Ensure minimum length
	if len(clean) < 3 {
		clean = "user" + clean // Fallback prefix
		if len(clean) > 20 {
			clean = clean[:20]
		}
	}

	return clean
}

func titleCase(s string) string {
	words := strings.Fields(s)
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(string(word[0])) + strings.ToLower(word[1:])
		}
	}
	return strings.Join(words, " ")
}

func SaveUserToDb(user User) error {
	// Insert the user into the database
	_, err := db.Exec("INSERT INTO users (email, nickname, avatar, created_at) VALUES ($1, $2, $3, $4)",
		user.Email, user.NickName, user.Avatar, user.CreatedAt)
	if err != nil {
		return err
	}
	return nil
}

func ReadfromDb() []User {
	var users []User
	rows, err := db.Query("SELECT id, email, nickname, avatar, created_at FROM users")
	if err != nil {
		log.Printf("Error querying users: %v", err)
		return nil
	}
	defer rows.Close()

	for rows.Next() {
		var user User
		if err := rows.Scan(&user.ID, &user.Email, &user.NickName, &user.Avatar, &user.CreatedAt); err != nil {
			log.Printf("Error scanning user row: %v", err)
			continue
		}
		users = append(users, user)
	}
	if err = rows.Err(); err != nil {
		log.Printf("Error iterating user rows: %v", err)
	}
	return users
}
