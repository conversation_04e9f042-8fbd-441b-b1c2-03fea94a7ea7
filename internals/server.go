package internals

import (
	"log"
	"net/http"
)

// EnableCORS middleware to handle CORS headers
func EnableCORS(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.<PERSON>er().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.<PERSON>er().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, X-Access-Key")
		w.Header().Set("Access-Control-Max-Age", "86400")

		if r.Method == "OPTIONS" {
			log.Printf("Handled OPTIONS preflight request for: %s", r.URL.Path)
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// SetupRoutes configures all the HTTP routes
func SetupRoutes() http.Handler {
	mux := http.NewServeMux()

	// API routes
	mux.HandleFunc("POST /api/entries", HandleSaveEntry)
	mux.HandleFunc("GET /api/entries", HandleGetEntries)
	mux.HandleFunc("GET /api/purposes", HandleGetPurposes)
	mux.HandleFunc("POST /api/purposes", HandleCreatePurpose)

	mux.HandleFunc("POST /api/chat/{purposeId}", HandleChat)
	mux.HandleFunc("GET /api/chat/{purposeId}/history", HandleGetChatHistory)

	// Authentication routes
	mux.HandleFunc("GET /auth/google", InitiateGoogleAuth)
	mux.HandleFunc("GET /auth/google/callback", HandleGoogleCallback)
	mux.HandleFunc("POST /auth/logout", HandleLogout)

	// User profile routes
	mux.HandleFunc("GET /api/user/profile", HandleGetUserProfile)
	mux.HandleFunc("POST /api/user/signout-all", HandleSignOutAllDevices)

	// Static file routes
	audioServer := http.StripPrefix("/audio/", http.FileServer(http.Dir(audioDir)))
	mux.Handle("/audio/", audioServer)
	mux.HandleFunc("/app.js", HandleAppJS)
	mux.HandleFunc("/styles/global.css", HandleGlobalCSS)
	mux.HandleFunc("/static/", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, r.URL.Path[1:])
	})
	mux.HandleFunc("/index.html", HandleIndexHTML)
	mux.HandleFunc("/app", HandleIndexHTML)
	mux.HandleFunc("/homepage.html", HandleHomeHTML)
	mux.HandleFunc("/home", HandleHomeHTML)
	mux.HandleFunc("/pricing.html", HandlePricingHTML)
	mux.HandleFunc("/pricing", HandlePricingHTML)
	mux.HandleFunc("/setting", HandleSettings)

	// Root route
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}
		HandleHomeHTML(w, r)
	})

	return EnableCORS(mux)
}

// StartServer starts the HTTP server on the specified port
func StartServer(port string) error {
	handler := SetupRoutes()
	serverAddr := ":" + port

	log.Printf("Starting server on %s", serverAddr)
	return http.ListenAndServe(serverAddr, handler)
}
