package internals

import (
	"database/sql"
	"log"
	"os"
	"strconv"
	"time"
)

const (
	defaultDBFile              = "./journal.db"
	defaultAudioDir            = "./audiofiles"
	defaultServerPort          = "8080"
	defaultGeminiModel         = "gemini-2.0-flash"
	defaultSummaryPrompt       = "Summarize the following journal entry concisely in one or two sentences: "
	defaultTranscriptionPrompt = "Transcribe the following audio recording:"
	defaultMaxUploadSizeMB     = 20
	defaultMaxEntriesForChat   = 5
	defaultMaxHistoryTurns     = 20
	defaultMaxHistoryMsgs      = 50
)

type Purpose struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	Description *string `json:"description"`
	UserID      *int    `json:"userId,omitempty"` // null for default purposes, user ID for user-specific purposes
}

type CreatePurposeRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type Entry struct {
	ID            int       `json:"id"`
	Timestamp     time.Time `json:"timestamp"`
	Transcription string    `json:"transcription"`
	Summary       string    `json:"summary"`
	AudioFilename string    `json:"audioFilename"`
	UserID        int       `json:"userId"`
	PurposeID     int       `json:"purposeId"`
	PurposeName   string    `json:"purposeName"`
	CreatedAt     time.Time `json:"createdAt"`
}

type ChatMessage struct {
	ID        int       `json:"id"`
	PurposeID int       `json:"purposeId"`
	Timestamp time.Time `json:"timestamp"`
	Role      string    `json:"role"`
	Content   string    `json:"content"`
}
type ChatRequest struct {
	Message string `json:"message"`
}
type ChatResponse struct {
	Reply string `json:"reply"`
}

var (
	db *sql.DB

	geminiAPIKey              string
	geminiModelName           string
	audioDir                  string
	summaryPrompt             string
	transcriptionPrompt       string
	maxUploadSizeMB           int
	maxEntriesForChatContext  int
	maxHistoryTurnsForContext int
	maxHistoryMessagesToFetch int
)

// GetDB returns the database connection
func GetDB() *sql.DB {
	return db
}

// SetDB sets the database connection
func SetDB(database *sql.DB) {
	db = database
}

// GetEnv gets an environment variable with a default value
func GetEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetEnvInt gets an environment variable as an integer with a default value
func GetEnvInt(key string, defaultValue int) int {
	valueStr := os.Getenv(key)
	if valueStr == "" {
		return defaultValue
	}

	value, err := strconv.Atoi(valueStr)
	if err != nil {
		log.Printf("Warning: Invalid integer value for %s: %s (using default: %d)", key, valueStr, defaultValue)
		return defaultValue
	}

	return value
}
