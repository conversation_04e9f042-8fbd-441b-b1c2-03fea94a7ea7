<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing - Voice Diary</title>
    <link rel="apple-touch-icon" sizes="180x180" href="/static/favicon/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="/static/favicon/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="/static/favicon/favicon-16x16.png">
<link rel="manifest" href="/static/favicon/site.webmanifest">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        // Standardized brand colors
                        azure: '#3b82f6',
                        white: '#ffffff',
                        black: '#000000',
                        'yellow-green': '#85cb33',
                        red: '#ff0000',
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        neutral: {
                            50: '#fafafa',
                            100: '#f5f5f5',
                            200: '#e5e5e5',
                            300: '#d4d4d4',
                            400: '#a3a3a3',
                            500: '#737373',
                            600: '#525252',
                            700: '#404040',
                            800: '#262626',
                            900: '#171717',
                        },
                        success: {
                            50: '#f0f9e8',
                            100: '#d9f0c4',
                            200: '#b8e69b',
                            300: '#95dc72',
                            400: '#85cb33',
                            500: '#6ba82a',
                            600: '#548521',
                            700: '#3d6218',
                            800: '#264010',
                            900: '#0f1d07'
                        },
                        error: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ff0000',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d'
                        }
                    }
                }
            }
        }
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="styles/global.css">
    <style>
        .gradient-bg {
            background: var(--gradient-hero);
        }

        .pricing-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .feature-check {
            color: var(--success-500);
        }

        .feature-x {
            color: var(--error-500);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .hero-gradient {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .coming-soon-banner {
            position: absolute;
            top: 12px;
            right: 12px;
            background: linear-gradient(135deg, #8b5cf6, #a855f7);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            z-index: 10;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--neutral-300);
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.slider {
            background-color: var(--primary-500);
        }

        input:checked+.slider:before {
            transform: translateX(26px);
        }

        /* Accordion Styles */
        .accordion-content {
            transition: all 0.3s ease;
        }

        .accordion-trigger:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .accordion-icon {
            transition: transform 0.2s ease;
        }
    </style>
</head>

<body class="text-gray-800 bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                  <div class="flex items-center space-x-2">
                    <img src="/static/logo.png" alt="Voice Diary Logo" class="w-8 h-8 rounded-lg object-contain">
                    <span class="text-xl font-bold text-gray-900">Voice Diary</span>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/home" class="text-gray-600 hover:text-azure transition-colors">Home</a>
                    <a href="#pricing" class="text-azure font-semibold">Pricing</a>
                    <a href="/app"
                        class="bg-azure text-white px-6 py-2 rounded-lg hover:bg-primary-600 transition-colors">Get
                        Started</a>
                </div>
                <button class="md:hidden p-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient text-black py-16">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-4xl text-black g:text-5xl font-bold mb-4">
                Simple, Transparent Pricing
            </h1>
            <p class="text-xl text-primary-500 mb-8 max-w-2xl mx-auto">
                Choose the perfect plan for your voice diary journey. Start free and upgrade when you're ready.
            </p>

            <!-- Billing Toggle -->
            <div class="flex items-center justify-center space-x-4 mb-8">
                <span class="text-primary-500" id="monthlyLabel">Monthly</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="billingToggle">
                    <span class="slider"></span>
                </label>
                <span class="text-primary-400" id="yearlyLabel">Yearly <span
                        class="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-semibold ml-1">Save
                        20%</span></span>
            </div>
        </div>
    </section>

    <!-- Pricing Cards -->
    <section id="pricing" class="py-16 -mt-8">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <!-- Free Plan -->
                <div class="bg-white p-8 rounded-xl shadow-lg pricing-card border-2 border-gray-200 relative">
                    <div class="coming-soon-banner">Coming Soon</div>
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Free</h3>
                        <div class="text-5xl font-bold text-gray-900 mb-2">
                            KSh 0
                        </div>
                        <p class="text-gray-600">Perfect for getting started</p>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">10 diary entries per month</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Basic AI transcription</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">1 purpose category</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Limited AI chat interactions</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Web access</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-x" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-400">Advanced AI insights</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-x" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-400">Data export functionality</span>
                        </li>
                    </ul>

                    <!-- <a href="/" class="block w-full text-center bg-gray-200 text-gray-800 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors">
                        Get Started Free
                    </a> -->
                </div>

                <!-- Pro Plan -->
                <div
                    class="bg-white p-8 rounded-xl shadow-lg pricing-card border-2 border-azure relative transform scale-105">
                    <div class="coming-soon-banner">Coming Soon</div>
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-azure text-white px-6 py-2 rounded-full text-sm font-semibold">Most
                            Popular</span>
                    </div>

                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Pro</h3>
                        <div class="text-5xl font-bold text-gray-900 mb-2">
                            <span id="proPrice">KSh 300</span><span class="text-lg text-gray-600">/month</span>
                        </div>
                        <p class="text-gray-600">For serious diary keepers</p>
                        <div id="proSavings" class="text-yellow-green text-sm font-semibold mt-1 hidden">Save KSh
                            600/year</div>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Unlimited diary entries</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Advanced AI insights & analysis</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Unlimited purpose categories</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Full AI chat functionality</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Data export functionality</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Priority support</span>
                        </li>
                    </ul>

                    <!-- <a href="/" class="block w-full text-center bg-azure text-white py-3 rounded-lg font-semibold hover:bg-primary-600 transition-colors">
                        Start Pro Trial
                    </a> -->
                </div>

                <!-- Enterprise Plan -->
                <div class="bg-white p-8 rounded-xl shadow-lg pricing-card border-2 border-gray-200 relative">
                    <div class="coming-soon-banner">Coming Soon</div>
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Enterprise</h3>
                        <div class="text-5xl font-bold text-gray-900 mb-2">Custom</div>
                        <p class="text-gray-600">For teams and organizations</p>
                    </div>

                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Everything in Pro</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Team management</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Advanced analytics</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">SSO integration</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Custom integrations</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">Dedicated support</span>
                        </li>
                        <li class="flex items-center space-x-3">
                            <svg class="w-5 h-5 feature-check" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                    clip-rule="evenodd" />
                            </svg>
                            <span class="text-gray-700">SLA guarantee</span>
                        </li>
                    </ul>

                    <!-- <a href="mailto:<EMAIL>" class="block w-full text-center bg-gray-200 text-gray-800 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors">
                        Contact Sales
                    </a> -->
                </div>
            </div>
        </div>
    </section>
    <!-- Feature Comparison Table -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Compare All Features</h2>
                <p class="text-xl text-gray-600">See exactly what's included in each plan</p>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full max-w-5xl mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Features</th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Free</th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900 bg-primary-50">Pro</th>
                            <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Enterprise</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Diary entries per month</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">10</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600 bg-primary-50">Unlimited</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">Unlimited</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">AI transcription</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">Basic</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600 bg-primary-50">Advanced</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">Advanced</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Purpose categories</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">1</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600 bg-primary-50">Unlimited</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">Unlimited</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">AI chat interactions</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">Limited</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600 bg-primary-50">Unlimited</td>
                            <td class="px-6 py-4 text-center text-sm text-gray-600">Unlimited</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Advanced AI insights</td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-x mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center bg-primary-50">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Data export functionality</td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-x mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center bg-primary-50">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Priority support</td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-x mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center bg-primary-50">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Team management</td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-x mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center bg-primary-50">
                                <svg class="w-5 h-5 feature-x mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Advanced analytics</td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-x mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center bg-primary-50">
                                <svg class="w-5 h-5 feature-x mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <svg class="w-5 h-5 feature-check mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd" />
                                </svg>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p class="text-xl text-gray-600">Everything you need to know about our pricing</p>
            </div>

            <div class="max-w-3xl mx-auto space-y-4">
                <!-- FAQ Item 1 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 accordion-item">
                    <button
                        class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors accordion-trigger"
                        data-target="faq-1">
                        <h3 class="text-lg font-semibold text-gray-900">Can I change plans anytime?</h3>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 accordion-icon"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="accordion-content hidden" id="faq-1">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600">Yes! You can upgrade or downgrade your plan at any time. Changes
                                take effect immediately, and we'll prorate any billing differences.</p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 accordion-item">
                    <button
                        class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors accordion-trigger"
                        data-target="faq-2">
                        <h3 class="text-lg font-semibold text-gray-900">Is there a free trial for Pro?</h3>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 accordion-icon"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="accordion-content hidden" id="faq-2">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600">Yes, we offer a 14-day free trial for the Pro plan. No credit card
                                required to start your trial.</p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 accordion-item">
                    <button
                        class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors accordion-trigger"
                        data-target="faq-3">
                        <h3 class="text-lg font-semibold text-gray-900">What happens to my data if I downgrade?</h3>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 accordion-icon"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="accordion-content hidden" id="faq-3">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600">Your data is always safe. If you downgrade, you'll keep all your
                                existing entries but new features will be limited according to your plan.</p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 accordion-item">
                    <button
                        class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors accordion-trigger"
                        data-target="faq-4">
                        <h3 class="text-lg font-semibold text-gray-900">Do you offer refunds?</h3>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 accordion-icon"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="accordion-content hidden" id="faq-4">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600">We offer a 30-day money-back guarantee for all paid plans. If
                                you're not satisfied, contact us for a full refund in KSh.</p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 accordion-item">
                    <button
                        class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors accordion-trigger"
                        data-target="faq-5">
                        <h3 class="text-lg font-semibold text-gray-900">Is my data secure?</h3>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 accordion-icon"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="accordion-content hidden" id="faq-5">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600">Absolutely. All your diary entries are encrypted and stored
                                securely. We never share your personal data with third parties.</p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 6 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 accordion-item">
                    <button
                        class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors accordion-trigger"
                        data-target="faq-6">
                        <h3 class="text-lg font-semibold text-gray-900">What payment methods do you accept?</h3>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 accordion-icon"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="accordion-content hidden" id="faq-6">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600">We accept USDC stablecoin payments on the Base.org Layer 2 network
                                for low fees and price stability. Traditional payment methods coming soon.</p>
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 7 -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 accordion-item">
                    <button
                        class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors accordion-trigger"
                        data-target="faq-7">
                        <h3 class="text-lg font-semibold text-gray-900">Why do you use cryptocurrency payments?</h3>
                        <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200 accordion-icon"
                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div class="accordion-content hidden" id="faq-7">
                        <div class="px-6 pb-4">
                            <p class="text-gray-600">We use Base.org (Ethereum Layer 2) with USDC stablecoin for global
                                accessibility, low transaction fees, and price stability. No crypto experience needed -
                                we'll guide you through the simple setup process.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-primary-600 text-white">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Start Your Voice Diary Journey?</h2>
            <p class="text-xl mb-8 text-primary-100 max-w-2xl mx-auto">
                Join the waitlist to be the first to know when Voice Diary launches with affordable KSh pricing.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/"
                    class="bg-white text-azure px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors shadow-lg">
                    Join Waitlist
                </a>
                <a href="mailto:<EMAIL>"
                    class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-azure transition-colors">
                    Contact Us
                </a>
            </div>
            <p class="mt-6 text-primary-200">Coming Soon • Affordable KSh pricing • 30-day money-back guarantee</p>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                      <div class="flex items-center space-x-2">
                    <img src="/static/logo.png" alt="Voice Diary Logo" class="w-8 h-8 rounded-lg object-contain">
                    <span class="text-xl font-bold text-gray-900">Voice Diary</span>
                </div>
                    <p class="text-gray-400 mb-6 max-w-md">
                        Transform your thoughts into insights with AI-powered voice diary. Your personal growth
                        companion.
                    </p>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Product</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="/homepage.html" class="hover:text-white transition-colors">Home</a></li>
                        <li><a href="/pricing" class="hover:text-white transition-colors">Pricing</a></li>
                        <li><a href="/app" class="hover:text-white transition-colors">Get Started</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-gray-400">
                        <!-- <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li> -->
                        <li><a href="mailto:<EMAIL>" class="hover:text-white transition-colors">Contact
                                Us</a></li>
                        
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2025 Voice Diary. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Dark Mode Toggle Script -->
    <script src="static/dark-mode-toggle.js"></script>

    <script>
        // Billing toggle functionality
        const billingToggle = document.getElementById('billingToggle');
        const proPrice = document.getElementById('proPrice');
        const proSavings = document.getElementById('proSavings');
        const monthlyLabel = document.getElementById('monthlyLabel');
        const yearlyLabel = document.getElementById('yearlyLabel');

        billingToggle.addEventListener('change', function () {
            if (this.checked) {
                // Yearly billing
                proPrice.textContent = 'KSh 3,000';
                proSavings.classList.remove('hidden');
                monthlyLabel.classList.add('text-primary-300');
                monthlyLabel.classList.remove('text-primary-800');
                yearlyLabel.classList.add('text-primary-800');
                yearlyLabel.classList.remove('text-primary-200');

                // Update price display to show yearly
                const priceSpan = proPrice.nextElementSibling;
                priceSpan.textContent = '/year';
            } else {
                // Monthly billing
                proPrice.textContent = 'KSh 300';
                proSavings.classList.add('hidden');
                monthlyLabel.classList.add('text-primary-800');
                monthlyLabel.classList.remove('text-primary-300');
                yearlyLabel.classList.add('text-primary-200');
                yearlyLabel.classList.remove('text-primary-800');

                // Update price display to show monthly
                const priceSpan = proPrice.nextElementSibling;
                priceSpan.textContent = '/month';
            }
        });

        // FAQ Accordion functionality
        document.addEventListener('DOMContentLoaded', function () {
            const accordionTriggers = document.querySelectorAll('.accordion-trigger');

            accordionTriggers.forEach(trigger => {
                trigger.addEventListener('click', function () {
                    const targetId = this.getAttribute('data-target');
                    const content = document.getElementById(targetId);
                    const icon = this.querySelector('.accordion-icon');
                    const allContents = document.querySelectorAll('.accordion-content');
                    const allIcons = document.querySelectorAll('.accordion-icon');

                    // Close all other accordions
                    allContents.forEach(otherContent => {
                        if (otherContent.id !== targetId) {
                            otherContent.classList.add('hidden');
                        }
                    });

                    // Reset all other icons
                    allIcons.forEach(otherIcon => {
                        if (otherIcon !== icon) {
                            otherIcon.classList.remove('rotate-180');
                        }
                    });

                    // Toggle current accordion
                    if (content.classList.contains('hidden')) {
                        content.classList.remove('hidden');
                        icon.classList.add('rotate-180');
                    } else {
                        content.classList.add('hidden');
                        icon.classList.remove('rotate-180');
                    }
                });
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>

</html>