# Voice Diary App

A purpose-driven diary application with voice recording, transcription, and AI-powered insights.

## Features

- Voice recording and transcription using Google Gemini API
- Purpose-specific diaries for different areas of your life
- AI-powered chat for insights based on your diary entries
- Multi-user support with access key authentication
- Works offline for recording, with online features when available

## Running with Docker

### Prerequisites

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)
- Google Gemini API key (get one from https://aistudio.google.com/apikey)

### Setup

1. Make sure your `.env` file contains your Google Gemini API key:

```
GEMINI_API_KEY=your_api_key_here
```

2. Build and start the application:

```bash
docker-compose up -d
```

3. Access the application at http://localhost:8080

### Data Persistence

The application stores data in:
- `journal.db`: SQLite database file
- `audiofiles/`: Directory for audio recordings

These are mounted as volumes in the Docker container, so your data persists even if you rebuild or restart the container.

## Running Without Docker

### Prerequisites

- Go 1.21 or later
- SQLite and GCC (for CGO support)
- Google Gemini API key

### Setup

1. Install dependencies:

```bash
go mod download
```

2. Run the application with CGO enabled:

```bash
# Using the provided script
./run.sh

# Or manually
CGO_ENABLED=1 go run main.go
```

3. Access the application at http://localhost:8080

### Building the Application

To build the application, use the provided script or manually enable CGO:

```bash
# Using the provided script
./build.sh

# Or manually
CGO_ENABLED=1 go build -o journal-app .
```

## Important Note About CGO

This application uses the `github.com/mattn/go-sqlite3` package which requires CGO to be enabled. If you encounter an error like:

```
Binary was compiled with 'CGO_ENABLED=0', go-sqlite3 requires cgo to work. This is a stub
```

Make sure to set `CGO_ENABLED=1` when building or running the application.

## Configuration

Configuration is managed through environment variables or a `.env` file:

| Variable | Description | Default |
|----------|-------------|---------|
| PORT | Server port | 8080 |
| DB_FILE | SQLite database path | ./journal.db |
| AUDIO_DIR | Directory for audio files | ./audiofiles |
| GEMINI_API_KEY | Google Gemini API key | (required) |
| GEMINI_MODEL | Gemini model to use | gemini-2.0-flash |
| MAX_UPLOAD_SIZE_MB | Maximum upload size in MB | 20 |
