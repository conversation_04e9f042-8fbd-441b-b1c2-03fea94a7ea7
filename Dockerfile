FROM golang:1.21-alpine AS builder

# Set working directory
WORKDIR /app

# Install necessary build tools and dependencies
RUN apk add --no-cache gcc musl-dev postgresql-dev

# Copy go.mod and go.sum files first for better caching
COPY go.mod go.sum ./
RUN go mod download

# Copy the rest of the application code
COPY . .

# Build the application with CGO enabled
RUN CGO_ENABLED=1 GOOS=linux go build -o journal-app .

# Create a smaller final image
FROM alpine:3.19

# Install runtime dependencies
RUN apk add --no-cache postgresql-libs ca-certificates tzdata

# Create a non-root user to run the application
RUN adduser -D -h /app appuser

# Set working directory
WORKDIR /app

# Copy the binary from the builder stage
COPY --from=builder /app/journal-app .

# Copy static files and templates
COPY --from=builder /app/index.html .
COPY --from=builder /app/settings.html .
COPY --from=builder /app/app.js .
COPY --from=builder /app/.env .

# Create directories for data persistence and set permissions
RUN mkdir -p /app/audiofiles && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose the application port
EXPOSE 8080

# Command to run the application
CMD ["./journal-app"]
