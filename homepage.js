<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Diary - Capture Your Thoughts, Speak Your Truth</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="apple-touch-icon" sizes="180x180" href="/static/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/static/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/favicon/favicon-16x16.png">
    <link rel="manifest" href="/static/favicon/site.webmanifest">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        neutral: {
                            50: '#fafafa',
                            100: '#f5f5f5',
                            200: '#e5e5e5',
                            300: '#d4d4d4',
                            400: '#a3a3a3',
                            500: '#737373',
                            600: '#525252',
                            700: '#404040',
                            800: '#262626',
                            900: '#171717',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }


       
        [data-theme="dark"] {
            color-scheme: dark;
        }

        [data-theme="dark"] body {
            background-color: #0f172a;
            color: #e2e8f0;
        }

       
        .hero-gradient {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        [data-theme="dark"] .hero-gradient {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
        }

        .card-gradient {
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
        }

        [data-theme="dark"] .card-gradient {
            background: linear-gradient(145deg, #1e293b 0%, #334155 100%);
            border: 1px solid #334155;
        }

       
        .audio-bar {
            background: linear-gradient(to top, #0ea5e9, #38bdf8);
            animation: audioWave 1.5s ease-in-out infinite alternate;
        }

        .audio-bar:nth-child(2) {
            animation-delay: 0.1s;
        }

        .audio-bar:nth-child(3) {
            animation-delay: 0.2s;
        }

        .audio-bar:nth-child(4) {
            animation-delay: 0.3s;
        }

        .audio-bar:nth-child(5) {
            animation-delay: 0.4s;
        }

        .audio-bar:nth-child(6) {
            animation-delay: 0.5s;
        }

        .audio-bar:nth-child(7) {
            animation-delay: 0.6s;
        }

        @keyframes audioWave {
            0% {
                height: 20%;
            }

            100% {
                height: 100%;
            }
        }

       
        .quote-background {
            position: relative;
        }

        .quote-background::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 10px;
            font-size: 120px;
            font-family: Georgia, serif;
            color: rgba(14, 165, 233, 0.1);
            line-height: 1;
            z-index: 0;
            pointer-events: none;
        }

        [data-theme="dark"] .quote-background::before {
            color: rgba(14, 165, 233, 0.15);
        }

        .quote-content {
            position: relative;
            z-index: 1;
        }

       
        * {
            transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

       
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        [data-theme="dark"] ::-webkit-scrollbar-track {
            background: #1e293b;
        }

        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        [data-theme="dark"] ::-webkit-scrollbar-thumb {
            background: #475569;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        [data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
            background: #64748b;
        }
    </style>
</head>

<body class="bg-slate-50 dark:bg-slate-900 text-slate-900 dark:text-slate-100">
    <!-- Navigation -->
    <nav
        class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-md border-b border-slate-200 dark:border-slate-700 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <span class="text-xl font-bold text-slate-900 dark:text-white">Voice Diary</span>
                </div>

                <div class="hidden md:flex items-center space-x-8">
                    <a href="#features"
                        class="text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">Features</a>
                    <a href="/pricing"
                        class="text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">Pricing</a>
                    <a href="#testimonials"
                        class="text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400 font-medium">Testimonials</a>

                    <button id="startJourneyBtn"
                        class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Start Your Journey
                    </button>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobileMenuBtn"
                        class="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-gradient py-20 lg:py-32">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div class="text-center lg:text-left">
                    <h1
                        class="text-4xl sm:text-5xl lg:text-6xl font-bold text-slate-900 dark:text-white leading-tight mb-6">
                        Voice <span class="text-primary-500">Diary</span>
                    </h1>
                    <p class="text-xl text-slate-600 dark:text-slate-300 mb-8 leading-relaxed">
                        Capture your thoughts, speak your truth, and discover yourself through the power of voice
                        journaling.
                    </p>

                    <!-- Carl Jung Quote -->
                    <blockquote
                        class="bg-white/50 dark:bg-slate-800/50 p-6 rounded-xl border-l-4 border-primary-500 mb-8 quote-background">
                        <div class="quote-content">
                            <p class="text-lg italic text-slate-700 dark:text-slate-300 mb-3">
                                "The soul demands your truth, not your effort to be good. It wants your most authentic
                                voice, not your censored one."
                            </p>
                            <cite class="text-sm font-medium text-slate-500 dark:text-slate-400">
                                — Inspired by Carl Jung's ideas in "The Red Book"
                            </cite>
                        </div>
                    </blockquote>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                        <button id="startJourneyBtnHero"
                            class="bg-primary-500 hover:bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors shadow-lg hover:shadow-xl">
                            Start Your Journey
                        </button>
                        <button id="learnMoreBtn"
                            class="border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors">
                            Learn More
                        </button>
                    </div>
                </div>

                <!-- Audio Visualizer Mockup -->
                <div class="flex justify-center lg:justify-end">
                    <div class="card-gradient p-8 rounded-2xl shadow-xl max-w-md w-full">
                        <div class="text-center mb-6">
                            <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">Voice Recording
                            </h3>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Speak your thoughts naturally</p>
                        </div>

                        <!-- Audio Visualizer -->
                        <div class="flex items-end justify-center space-x-1 h-24 mb-6">
                            <div class="audio-bar w-2 bg-primary-500 rounded-full"></div>
                            <div class="audio-bar w-2 bg-primary-500 rounded-full"></div>
                            <div class="audio-bar w-2 bg-primary-500 rounded-full"></div>
                            <div class="audio-bar w-2 bg-primary-500 rounded-full"></div>
                            <div class="audio-bar w-2 bg-primary-500 rounded-full"></div>
                            <div class="audio-bar w-2 bg-primary-500 rounded-full"></div>
                            <div class="audio-bar w-2 bg-primary-500 rounded-full"></div>
                        </div>

                        <div class="text-center">
                            <div class="text-2xl font-mono text-slate-700 dark:text-slate-300 mb-4">02:34</div>
                            <button
                                class="w-16 h-16 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center shadow-lg transition-colors">
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z"
                                        clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-white dark:bg-slate-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">Features</h2>
                <p class="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
                    Everything you need to capture, reflect, and grow through voice journaling
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Voice Recording -->
                <div class="text-center">
                    <div
                        class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Voice Recording</h3>
                    <p class="text-slate-600 dark:text-slate-400">
                        Record your thoughts with crystal-clear audio quality and real-time visualization
                    </p>
                </div>

                <!-- Transcription -->
                <div class="text-center">
                    <div
                        class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Transcription</h3>
                    <p class="text-slate-600 dark:text-slate-400">
                        Automatic speech-to-text conversion with high accuracy for easy review and search
                    </p>
                </div>

                <!-- Mood Tracking -->
                <div class="text-center">
                    <div
                        class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Mood Tracking</h3>
                    <p class="text-slate-600 dark:text-slate-400">
                        Track your emotional journey and discover patterns in your thoughts and feelings
                    </p>
                </div>

                <!-- Private & Secure -->
                <div class="text-center">
                    <div
                        class="w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-primary-600 dark:text-primary-400" fill="currentColor"
                            viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-slate-900 dark:text-white mb-2">Private & Secure</h3>
                    <p class="text-slate-600 dark:text-slate-400">
                        Your thoughts are encrypted and stored securely. Complete privacy guaranteed
                    </p>
                </div>
            </div>
        </div>
    </section>

    <section id="testimonials" class="py-20 bg-white dark:bg-slate-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl sm:text-4xl font-bold text-slate-900 dark:text-white mb-4">What Our Users Say</h2>
                <p class="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
                    Discover how Voice Diary is transforming lives through authentic self-expression
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card-gradient rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div
                            class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-4">
                            <span class="text-primary-600 dark:text-primary-400 font-semibold">AW</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-slate-900 dark:text-white">Amina Wanjiku</h4>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Writer</p>
                        </div>
                    </div>
                    <p class="text-slate-700 dark:text-slate-300 italic">
                        "Voice Diary has been life-changing for me. Being able to speak my thoughts freely without
                        judgment has helped me discover parts of myself I never knew existed."
                    </p>
                </div>

                <div class="card-gradient rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div
                            class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-4">
                            <span class="text-primary-600 dark:text-primary-400 font-semibold">DK</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-slate-900 dark:text-white">David Kipng'eno</h4>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Therapist</p>
                        </div>
                    </div>
                    <p class="text-slate-700 dark:text-slate-300 italic">
                        "I recommend Voice Diary to all my clients. The ability to capture authentic emotions through
                        voice creates a deeper connection with one's inner self."
                    </p>
                </div>

                <div class="card-gradient rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div
                            class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center mr-4">
                            <span class="text-primary-600 dark:text-primary-400 font-semibold">GM</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-slate-900 dark:text-white">Grace Muthoni</h4>
                            <p class="text-sm text-slate-600 dark:text-slate-400">Student</p>
                        </div>
                    </div>
                    <p class="text-slate-700 dark:text-slate-300 italic">
                        "The transcription feature is amazing! I can review my thoughts later and see patterns in my
                        thinking. It's like having a conversation with my future self."
                    </p>
                </div>
            </div>
        </div>
    </section>

    <footer class="bg-slate-900 dark:bg-slate-950 text-slate-300">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">

                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z"
                                    clip-rule="evenodd" />
                            </svg>
                        </div>
                        <span class="text-xl font-bold text-white">Voice Diary</span>
                    </div>
                    <p class="text-slate-400 mb-4 max-w-md">
                        Capture your thoughts, speak your truth, and discover yourself through the power of voice
                        journaling.
                    </p>
                    <div class="flex space-x-4">
                        <a href="https://linkedin.com/in/bravinyats" target="_blank"
                            rel="noopener noreferrer" class="text-slate-400 hover:text-white transition-colors"
                            title="LinkedIn">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
                            </svg>
                        </a>
                        <a href="https://github.com/bravinyats" target="_blank" rel="noopener noreferrer"
                            class="text-slate-400 hover:text-white transition-colors" title="GitHub">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                            </svg>
                        </a>
                        <a href="mailto:<EMAIL>" class="text-slate-400 hover:text-white transition-colors"
                            title="Email">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" />
                            </svg>
                        </a>
                    </div>
                </div>



            </div>

            <div class="border-t border-slate-800 mt-8 pt-8 text-center">
                <p class="text-slate-400">
                    © 2025 Voice Diary. All rights reserved.
                </p>
            </div>
        </div>
    </footer>



    <script>

        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const mobileMenu = document.createElement('div');
        mobileMenu.className = 'md:hidden bg-white dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 hidden';
        mobileMenu.innerHTML = `
            <div class="px-4 py-2 space-y-2">
                <a href="#features" class="block py-2 text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400">Features</a>
                <a href="/pricing" class="block py-2 text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400">Pricing</a>
                <a href="#testimonials" class="block py-2 text-slate-600 dark:text-slate-300 hover:text-primary-600 dark:hover:text-primary-400">Testimonials</a>
                <button id ="startJourneyBtnMobile" class="w-full mt-2 bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Start Your Journey
                </button>
            </div>
        `;

        document.querySelector('nav').appendChild(mobileMenu);

        mobileMenuBtn.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });


        const ctaButtons = document.querySelectorAll('#startJourneyBtn, #startJourneyBtnHero, #finalCtaBtn, #startJourneyBtnMobile');
        ctaButtons.forEach(btn => {
            btn.addEventListener('click', () => {

                window.location.href = '/app';
            });
        });


        document.getElementById('learnMoreBtn').addEventListener('click', () => {
            document.getElementById('features').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        });
    </script>
</body>

</html>