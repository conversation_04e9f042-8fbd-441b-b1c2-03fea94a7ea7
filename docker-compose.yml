version: '3.8'

services:
  journal-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: journal-app
    ports:
      - "8080:8080"
    volumes:
      # Mount this directory for data persistence
      - ./audiofiles:/app/audiofiles
    environment:
      # Override environment variables if needed
      - PORT=8080
      - DB_TYPE=postgres
      - DB_HOST=europe-west1-001.proxy.kinsta.app
      - DB_PORT=30514
      - DB_USER=leech
      - DB_PASSWORD=dW4=wR6=nC2_sP1+lY8_
      - DB_NAME=institutional-plum-camel
      - DB_SSL_MODE=disable
      - AUDIO_DIR=/app/audiofiles
      # You can override the Gemini API key here or use the one in .env
      # - GEMINI_API_KEY=your_api_key_here
    restart: unless-stopped
