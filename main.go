package main

import (
	"flag"
	"log"

	"github.com/user/journal/internals"
)

func main() {
	// Initialize configuration
	if err := internals.InitConfig(); err != nil {
		log.Fatalf("Error initializing configuration: %v", err)
	}

	// Parse command line flags
	port := flag.String("port", internals.GetEnv("PORT", internals.GetDefaultServerPort()), "Port to run the server on")
	dropTables := flag.Bool("drop-tables", false, "Drop all database tables (DANGEROUS - for development only)")
	dbconf := internals.Dbconfig
	flag.Parse()

	// Initialize database
	if err := internals.InitDatabase(dbconf.Type, dbconf.Host, dbconf.Port, dbconf.User, dbconf.Password, dbconf.Name, dbconf.SSLMode); err != nil {
		log.Fatalf("Error initializing database: %v", err)
	}
	defer internals.GetDB().Close()

	// Handle drop tables command
	if *dropTables {
		log.Println("⚠️  WARNING: Dropping all database tables...")
		if err := internals.DropAllTables(); err != nil {
			log.Fatalf("Error dropping tables: %v", err)
		}
		log.Println("✅ All tables dropped successfully. Exiting...")
		return
	}

	// Start server
	if err := internals.StartServer(*port); err != nil {
		log.Fatalf("Error starting server: %v", err)
	}
}
