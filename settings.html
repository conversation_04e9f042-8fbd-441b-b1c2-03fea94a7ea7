<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="apple-touch-icon" sizes="180x180" href="/static/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/static/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/favicon/favicon-16x16.png">
    <link rel="manifest" href="/static/favicon/site.webmanifest">
    <title>Settings - Voice Diary App</title>
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm p-4">
        <a href="/" class="text-blue-600 hover:text-blue-800">← Back to Home</a>
    </nav>
    <div class="container mx-auto p-4 md:p-8 max-w-2xl">
        <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-8">User Settings</h1>


        <div id="statusMessage" class="hidden mb-6 p-4 rounded-md"></div>


        <div id="loadingState" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading profile...</p>
        </div>

    
        <div id="profileSection" class="hidden">
            <div class="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-8">
                <h2 class="text-xl font-bold mb-6">Your Profile</h2>

                <div class="flex items-center space-x-6 mb-6">
                    <img id="userAvatar" src="" alt="Profile Picture" class="w-20 h-20 rounded-full border-2 border-gray-200">
                    <div>
                        <h3 id="userNickname" class="text-lg font-semibold text-gray-900"></h3>
                        <p id="userEmail" class="text-gray-600"></p>
                        <p id="memberSince" class="text-sm text-gray-500"></p>
                    </div>
                </div>

                

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-700 mb-2">Session Information</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Active Sessions:</span>
                                <span id="activeSessions" class="font-medium"></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Current Device:</span>
                                <span class="text-green-600 font-medium">This Device</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mb-8">
                <h2 class="text-xl font-bold mb-6">Security & Sessions</h2>

                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div>
                            <h3 class="font-semibold text-yellow-800">Sign Out From All Devices</h3>
                            <p class="text-sm text-yellow-700 mt-1">This will end all active sessions on all devices. You'll need to sign in again.</p>
                        </div>
                        <button id="signOutAllBtn" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Sign Out All
                        </button>
                    </div>
                </div>
            </div>

            <div class="bg-blue-50 p-6 rounded-lg border border-blue-200">
                <h3 class="font-bold text-blue-800 mb-3">About Your Account</h3>
                <ul class="list-disc pl-5 text-blue-700 space-y-2">
                    <li>Your account is secured with Google OAuth authentication</li>
                    <li>All your journal entries and purposes are private to your account</li>
                    <li>Sessions automatically expire based on your "Remember me" preference</li>
                    <li>You can sign out from all devices at any time for security</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = window.location.origin;

        document.addEventListener('DOMContentLoaded', () => {
            loadUserProfile();
            setupEventListeners();
        });


        function showStatus(message, isError = false) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message;
            statusMessage.className = `mb-6 p-4 rounded-md ${isError ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`;
            statusMessage.classList.remove('hidden');

            window.scrollTo(0, 0);


            if (!isError) {
                setTimeout(() => {
                    statusMessage.classList.add('hidden');
                }, 5000);
            }
        }

        async function loadUserProfile() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/user/profile`);

                if (!response.ok) {
                    if (response.status === 401) {

                        window.location.href = '/';
                        return;
                    }
                    throw new Error(`HTTP ${response.status}`);
                }

                const profile = await response.json();
                displayProfile(profile);

            } catch (error) {
                console.error('Error loading profile:', error);
                showStatus('Error loading profile. Please try refreshing the page.', true);
            }
        }

        // Display profile information
        function displayProfile(profile) {
            // Hide loading state and show profile
            document.getElementById('loadingState').classList.add('hidden');
            document.getElementById('profileSection').classList.remove('hidden');

            // Populate profile data
            document.getElementById('userAvatar').src = profile.avatar || '/default-avatar.png';
            document.getElementById('userNickname').textContent = profile.nickname || 'Unknown User';
            document.getElementById('userEmail').textContent = profile.email || 'No email';
            document.getElementById('activeSessions').textContent = profile.active_sessions || '0';

            // Format and display member since date
            if (profile.created_at) {
                const createdDate = new Date(profile.created_at);
                const formattedDate = createdDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                document.getElementById('memberSince').textContent = `Member since ${formattedDate}`;
            }
        }


        function setupEventListeners() {
            const signOutAllBtn = document.getElementById('signOutAllBtn');

            signOutAllBtn.addEventListener('click', async () => {
                if (!confirm('Are you sure you want to sign out from all devices? You will need to sign in again on all devices.')) {
                    return;
                }

                try {
                    signOutAllBtn.disabled = true;
                    signOutAllBtn.textContent = 'Signing Out...';

                    const response = await fetch(`${API_BASE_URL}/api/user/signout-all`, {
                        method: 'POST'
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const result = await response.json();
                    showStatus(`${result.message} (${result.sessions_deleted} sessions ended)`);

                    // Redirect to home page after a short delay
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);

                } catch (error) {
                    console.error('Error signing out from all devices:', error);
                    showStatus('Error signing out from all devices. Please try again.', true);

                    // Reset button state
                    signOutAllBtn.disabled = false;
                    signOutAllBtn.textContent = 'Sign Out All';
                }
            });
        }
    </script>
</body>
</html>