#!/bin/bash

# Ensure CGO is enabled when running the application
export CGO_ENABLED=1

# Load environment variables from .env file
if [ -f .env ]; then
  export $(grep -v '^#' .env | xargs)
fi

# Set PostgreSQL environment variables if not already set
export DB_TYPE=${DB_TYPE:-postgres}
export DB_HOST=${DB_HOST:-europe-west1-001.proxy.kinsta.app}
export DB_PORT=${DB_PORT:-30514}
export DB_USER=${DB_USER:-leech}
export DB_PASSWORD=${DB_PASSWORD:-dW4=wR6=nC2_sP1+lY8_}
export DB_NAME=${DB_NAME:-institutional-plum-camel}
export DB_SSL_MODE=${DB_SSL_MODE:-disable}

# Run the application
go run main.go
